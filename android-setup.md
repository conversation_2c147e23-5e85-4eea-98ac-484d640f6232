# إعداد تطبيق أندرويد لساعة المسجد

## الطرق المتاحة لإنشاء تطبيق أندرويد

### 1. PWA (Progressive Web App) - الأسهل والأسرع

#### المتطلبات:
- متصفح Chrome أو Edge على الهاتف
- اتصال بالإنترنت للتثبيت الأولي

#### خطوات التثبيت:
1. افتح الموقع في متصفح Chrome
2. انقر على "تثبيت التطبيق" عند ظهور المطالبة
3. أو من قائمة المتصفح اختر "إضافة إلى الشاشة الرئيسية"

#### المميزات:
- ✅ سهولة التثبيت
- ✅ تحديثات تلقائية
- ✅ يعمل بدون إنترنت بعد التثبيت
- ✅ إشعارات مواقيت الصلاة
- ✅ شاشة كاملة

### 2. Apache Cordova/PhoneGap

#### إنشاء مشروع Cordova:

```bash
# تثبيت Cordova
npm install -g cordova

# إنشاء مشروع جديد
cordova create MosqueClock com.mosque.clock "ساعة المسجد"
cd MosqueClock

# إضافة منصة أندرويد
cordova platform add android

# إضافة الإضافات المطلوبة
cordova plugin add cordova-plugin-device
cordova plugin add cordova-plugin-splashscreen
cordova plugin add cordova-plugin-statusbar
cordova plugin add cordova-plugin-whitelist
cordova plugin add cordova-plugin-local-notification
cordova plugin add cordova-plugin-vibration
cordova plugin add cordova-plugin-screen-orientation
cordova plugin add cordova-plugin-fullscreen
cordova plugin add cordova-plugin-keep-awake
```

#### ملف config.xml:

```xml
<?xml version='1.0' encoding='utf-8'?>
<widget id="com.mosque.clock" version="2.0.0" xmlns="http://www.w3.org/ns/widgets">
    <name>ساعة المسجد</name>
    <description>تطبيق شامل لعرض مواقيت الصلاة والساعة للمساجد</description>
    <author email="<EMAIL>" href="https://mosqueclock.com">
        فريق ساعة المسجد
    </author>
    
    <content src="index.html" />
    
    <preference name="DisallowOverscroll" value="true" />
    <preference name="android-minSdkVersion" value="22" />
    <preference name="android-targetSdkVersion" value="33" />
    <preference name="Orientation" value="default" />
    <preference name="Fullscreen" value="true" />
    <preference name="KeepRunning" value="true" />
    <preference name="ShowSplashScreenSpinner" value="false" />
    <preference name="SplashScreenDelay" value="3000" />
    <preference name="AutoHideSplashScreen" value="true" />
    
    <platform name="android">
        <icon density="ldpi" src="icons/android/icon-36-ldpi.png" />
        <icon density="mdpi" src="icons/android/icon-48-mdpi.png" />
        <icon density="hdpi" src="icons/android/icon-72-hdpi.png" />
        <icon density="xhdpi" src="icons/android/icon-96-xhdpi.png" />
        <icon density="xxhdpi" src="icons/android/icon-144-xxhdpi.png" />
        <icon density="xxxhdpi" src="icons/android/icon-192-xxxhdpi.png" />
        
        <splash density="port-ldpi" src="splash/android/screen-ldpi-portrait.png" />
        <splash density="port-mdpi" src="splash/android/screen-mdpi-portrait.png" />
        <splash density="port-hdpi" src="splash/android/screen-hdpi-portrait.png" />
        <splash density="port-xhdpi" src="splash/android/screen-xhdpi-portrait.png" />
        <splash density="port-xxhdpi" src="splash/android/screen-xxhdpi-portrait.png" />
        <splash density="port-xxxhdpi" src="splash/android/screen-xxxhdpi-portrait.png" />
        
        <allow-intent href="market:*" />
    </platform>
    
    <access origin="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
</widget>
```

#### بناء التطبيق:

```bash
# نسخ ملفات الويب
cp -r ../path/to/web/files/* www/

# بناء التطبيق
cordova build android

# تشغيل على الجهاز
cordova run android

# بناء للإنتاج
cordova build android --release
```

### 3. Capacitor (الأحدث والأفضل)

#### إعداد Capacitor:

```bash
# تثبيت Capacitor
npm install @capacitor/core @capacitor/cli

# تهيئة المشروع
npx cap init "ساعة المسجد" "com.mosque.clock"

# إضافة منصة أندرويد
npm install @capacitor/android
npx cap add android

# إضافة الإضافات
npm install @capacitor/local-notifications
npm install @capacitor/status-bar
npm install @capacitor/splash-screen
npm install @capacitor/device
npm install @capacitor/screen-orientation
```

#### ملف capacitor.config.ts:

```typescript
import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.mosque.clock',
  appName: 'ساعة المسجد',
  webDir: 'dist',
  bundledWebRuntime: false,
  plugins: {
    LocalNotifications: {
      smallIcon: "ic_stat_icon_config_sample",
      iconColor: "#488AFF",
      sound: "beep.wav",
    },
    SplashScreen: {
      launchShowDuration: 3000,
      launchAutoHide: true,
      backgroundColor: "#fffbfb",
      androidSplashResourceName: "splash",
      androidScaleType: "CENTER_CROP",
      showSpinner: false,
      androidSpinnerStyle: "large",
      iosSpinnerStyle: "small",
      spinnerColor: "#999999",
      splashFullScreen: true,
      splashImmersive: true,
    },
    StatusBar: {
      style: "DARK",
      backgroundColor: "#4a3b3b",
    },
  },
  android: {
    allowMixedContent: true,
    captureInput: true,
    webContentsDebuggingEnabled: true,
  }
};

export default config;
```

#### بناء التطبيق:

```bash
# بناء الويب
npm run build

# نسخ الملفات
npx cap copy

# فتح في Android Studio
npx cap open android

# أو بناء مباشرة
npx cap run android
```

### 4. Android TV Setup

#### إعدادات خاصة بالتلفاز:

```xml
<!-- في AndroidManifest.xml -->
<uses-feature
    android:name="android.software.leanback"
    android:required="false" />

<uses-feature
    android:name="android.hardware.touchscreen"
    android:required="false" />

<activity
    android:name=".MainActivity"
    android:banner="@drawable/banner"
    android:icon="@drawable/icon"
    android:label="@string/app_name"
    android:screenOrientation="landscape"
    android:theme="@style/AppTheme.NoActionBar">
    
    <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
        <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
    </intent-filter>
</activity>
```

## ملفات الأيقونات المطلوبة

### أحجام الأيقونات لأندرويد:
- 36x36 (ldpi)
- 48x48 (mdpi)
- 72x72 (hdpi)
- 96x96 (xhdpi)
- 144x144 (xxhdpi)
- 192x192 (xxxhdpi)

### أحجام شاشات البداية:
- 320x426 (ldpi)
- 320x470 (mdpi)
- 480x640 (hdpi)
- 720x960 (xhdpi)
- 960x1280 (xxhdpi)
- 1280x1920 (xxxhdpi)

## إعدادات الأذونات

```xml
<!-- في AndroidManifest.xml -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
<uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
```

## التوقيع والنشر

### إنشاء مفتاح التوقيع:

```bash
keytool -genkey -v -keystore mosque-clock.keystore -alias mosque-clock -keyalg RSA -keysize 2048 -validity 10000
```

### توقيع التطبيق:

```bash
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore mosque-clock.keystore app-release-unsigned.apk mosque-clock
zipalign -v 4 app-release-unsigned.apk MosqueClock.apk
```

## الاختبار

### اختبار على أجهزة مختلفة:
1. هواتف أندرويد (مختلف الأحجام)
2. أجهزة لوحية
3. Android TV
4. اختبار الاتجاهات (أفقي/عمودي)
5. اختبار الأداء والذاكرة

### أدوات الاختبار:
- Android Studio Emulator
- Firebase Test Lab
- BrowserStack
- Physical devices

## النشر

### Google Play Store:
1. إنشاء حساب مطور
2. رفع APK/AAB
3. إضافة الوصف والصور
4. تحديد الفئة والأذونات
5. النشر

### التوزيع المباشر:
- رفع APK على الموقع
- QR Code للتحميل
- توزيع عبر البريد الإلكتروني
