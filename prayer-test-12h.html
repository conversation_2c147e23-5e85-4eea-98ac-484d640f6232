<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام مواقيت الصلاة بنظام 12 ساعة</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        
        h1, h2 {
            color: #333;
            text-align: center;
        }
        
        .prayer-times {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            margin: 20px 0;
        }
        
        .prayer-time {
            text-align: center;
            margin: 10px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            width: 120px;
        }
        
        .prayer-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .prayer-hour {
            font-size: 1.2em;
            color: #4a3b3b;
        }
        
        .settings {
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        
        .settings h2 {
            margin-top: 0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        select, input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        button {
            background-color: #4a3b3b;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
        }
        
        button:hover {
            background-color: #71d3ee;
        }
        
        .manual-adjustments {
            margin-top: 20px;
        }
        
        .manual-time {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .manual-time label {
            width: 80px;
            margin-bottom: 0;
        }
        
        .manual-time input {
            flex: 1;
            margin: 0 10px;
        }
        
        .next-prayer {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9f7fe;
            border-radius: 5px;
            text-align: center;
        }
        
        .next-prayer h2 {
            margin-top: 0;
            color: #0078d4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار نظام مواقيت الصلاة بنظام 12 ساعة</h1>
        
        <div class="form-group">
            <label for="city-select">المدينة:</label>
            <select id="city-select">
                <option value="عمان">عمان</option>
                <option value="إربد">إربد</option>
                <option value="الزرقاء">الزرقاء</option>
                <option value="العقبة">العقبة</option>
                <option value="الرياض">الرياض</option>
                <option value="مكة المكرمة">مكة المكرمة</option>
                <option value="المدينة المنورة">المدينة المنورة</option>
                <option value="القاهرة">القاهرة</option>
            </select>
        </div>
        
        <div class="next-prayer" id="next-prayer-container">
            <h2>الصلاة القادمة</h2>
            <div id="next-prayer-name" class="prayer-name">-</div>
            <div id="next-prayer-time" class="prayer-hour">-</div>
        </div>
        
        <div class="prayer-times" id="prayer-times-container">
            <!-- سيتم إنشاء هذا القسم ديناميكيًا -->
        </div>
        
        <div class="settings">
            <h2>الإعدادات</h2>
            
            <div class="form-group">
                <label for="calculation-method">طريقة الحساب:</label>
                <select id="calculation-method">
                    <option value="Jordan">الأردن</option>
                    <option value="MWL">رابطة العالم الإسلامي</option>
                    <option value="ISNA">جمعية أمريكا الشمالية الإسلامية</option>
                    <option value="Egypt">دار الإفتاء المصرية</option>
                    <option value="Makkah">جامعة أم القرى بمكة</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="juristic-method">المذهب الفقهي (للعصر):</label>
                <select id="juristic-method">
                    <option value="Shafi">الشافعي/المالكي/الحنبلي</option>
                    <option value="Hanafi">الحنفي</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="enable-adhan" checked> تفعيل الأذان
                </label>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="enable-darkness" checked> تفعيل التعتيم
                </label>
            </div>
            
            <button id="save-settings">حفظ الإعدادات</button>
        </div>
        
        <div class="manual-adjustments">
            <h2>التعديل اليدوي للمواقيت</h2>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="enable-manual-adjustments"> تفعيل التعديل اليدوي
                </label>
            </div>
            
            <div class="manual-time">
                <label for="manual-fajr">الفجر:</label>
                <input type="text" id="manual-fajr" placeholder="00:00 ص/م">
                <button class="edit-time" data-prayer="fajr">تعديل</button>
            </div>
            
            <div class="manual-time">
                <label for="manual-sunrise">الشروق:</label>
                <input type="text" id="manual-sunrise" placeholder="00:00 ص/م">
                <button class="edit-time" data-prayer="sunrise">تعديل</button>
            </div>
            
            <div class="manual-time">
                <label for="manual-dhuhr">الظهر:</label>
                <input type="text" id="manual-dhuhr" placeholder="00:00 ص/م">
                <button class="edit-time" data-prayer="dhuhr">تعديل</button>
            </div>
            
            <div class="manual-time">
                <label for="manual-asr">العصر:</label>
                <input type="text" id="manual-asr" placeholder="00:00 ص/م">
                <button class="edit-time" data-prayer="asr">تعديل</button>
            </div>
            
            <div class="manual-time">
                <label for="manual-maghrib">المغرب:</label>
                <input type="text" id="manual-maghrib" placeholder="00:00 ص/م">
                <button class="edit-time" data-prayer="maghrib">تعديل</button>
            </div>
            
            <div class="manual-time">
                <label for="manual-isha">العشاء:</label>
                <input type="text" id="manual-isha" placeholder="00:00 ص/م">
                <button class="edit-time" data-prayer="isha">تعديل</button>
            </div>
            
            <button id="save-manual-times">حفظ جميع المواقيت</button>
            <button id="reset-manual-times">إعادة ضبط المواقيت</button>
        </div>
    </div>

    <!-- استدعاء ملفات النظام -->
    <script src="prayer-times-accurate.js"></script>
    <script src="prayer-manager.js"></script>
    
    <script>
        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة نظام مواقيت الصلاة
            PrayerManager.initialize();
            
            // عرض مواقيت الصلاة
            displayPrayerTimes();
            
            // عرض الصلاة القادمة
            displayNextPrayer();
            
            // إعداد مستمعي الأحداث
            setupEventListeners();
            
            // تحديث مواقيت الصلاة كل دقيقة
            setInterval(function() {
                displayNextPrayer();
            }, 60 * 1000);
        });
        
        // عرض مواقيت الصلاة
        function displayPrayerTimes() {
            const cityName = document.getElementById('city-select').value;
            const times = PrayerManager.getPrayerTimes(cityName);
            
            if (!times) {
                console.error(`لم يتم العثور على مواقيت لمدينة ${cityName}`);
                return;
            }
            
            const container = document.getElementById('prayer-times-container');
            container.innerHTML = '';
            
            const prayers = [
                { name: 'fajr', arabicName: 'الفجر' },
                { name: 'sunrise', arabicName: 'الشروق' },
                { name: 'dhuhr', arabicName: 'الظهر' },
                { name: 'asr', arabicName: 'العصر' },
                { name: 'maghrib', arabicName: 'المغرب' },
                { name: 'isha', arabicName: 'العشاء' }
            ];
            
            for (const prayer of prayers) {
                const div = document.createElement('div');
                div.className = 'prayer-time';
                
                const nameDiv = document.createElement('div');
                nameDiv.className = 'prayer-name';
                nameDiv.textContent = prayer.arabicName;
                
                const timeDiv = document.createElement('div');
                timeDiv.className = 'prayer-hour';
                timeDiv.textContent = times[prayer.name];
                
                div.appendChild(nameDiv);
                div.appendChild(timeDiv);
                container.appendChild(div);
                
                // تحديث حقول التعديل اليدوي
                const manualInput = document.getElementById(`manual-${prayer.name}`);
                if (manualInput) {
                    manualInput.value = times[prayer.name];
                }
            }
        }
        
        // عرض الصلاة القادمة
        function displayNextPrayer() {
            const cityName = document.getElementById('city-select').value;
            const nextPrayer = PrayerManager.getNextPrayer(cityName);
            
            if (!nextPrayer) {
                console.error(`لم يتم العثور على الصلاة القادمة لمدينة ${cityName}`);
                return;
            }
            
            document.getElementById('next-prayer-name').textContent = nextPrayer.arabicName;
            document.getElementById('next-prayer-time').textContent = nextPrayer.time;
        }
        
        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تغيير المدينة
            document.getElementById('city-select').addEventListener('change', function() {
                PrayerManager.settings.selectedCity = this.value;
                PrayerManager.saveSettings();
                displayPrayerTimes();
                displayNextPrayer();
            });
            
            // حفظ الإعدادات
            document.getElementById('save-settings').addEventListener('click', function() {
                PrayerManager.settings.calculationMethod = document.getElementById('calculation-method').value;
                PrayerManager.settings.juristicMethod = document.getElementById('juristic-method').value;
                PrayerManager.settings.adhanEnabled = document.getElementById('enable-adhan').checked;
                PrayerManager.settings.darknessEnabled = document.getElementById('enable-darkness').checked;
                
                PrayerManager.saveSettings();
                
                // تحديث مواقيت الصلاة
                displayPrayerTimes();
                displayNextPrayer();
                
                alert('تم حفظ الإعدادات بنجاح');
            });
            
            // تفعيل/تعطيل التعديل اليدوي
            document.getElementById('enable-manual-adjustments').addEventListener('change', function() {
                PrayerManager.settings.manualAdjustmentsEnabled = this.checked;
                PrayerManager.saveSettings();
                displayPrayerTimes();
                displayNextPrayer();
            });
            
            // أزرار تعديل الأوقات
            const editButtons = document.querySelectorAll('.edit-time');
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const prayer = this.getAttribute('data-prayer');
                    const input = document.getElementById(`manual-${prayer}`);
                    const time = input.value;
                    
                    if (time) {
                        const cityName = document.getElementById('city-select').value;
                        PrayerManager.setManualAdjustment(cityName, prayer, time);
                        displayPrayerTimes();
                        displayNextPrayer();
                    }
                });
            });
            
            // حفظ جميع المواقيت
            document.getElementById('save-manual-times').addEventListener('click', function() {
                const cityName = document.getElementById('city-select').value;
                const prayers = ['fajr', 'sunrise', 'dhuhr', 'asr', 'maghrib', 'isha'];
                
                for (const prayer of prayers) {
                    const input = document.getElementById(`manual-${prayer}`);
                    const time = input.value;
                    
                    if (time) {
                        PrayerManager.setManualAdjustment(cityName, prayer, time);
                    }
                }
                
                PrayerManager.settings.manualAdjustmentsEnabled = true;
                document.getElementById('enable-manual-adjustments').checked = true;
                PrayerManager.saveSettings();
                
                displayPrayerTimes();
                displayNextPrayer();
                
                alert('تم حفظ جميع المواقيت بنجاح');
            });
            
            // إعادة ضبط المواقيت
            document.getElementById('reset-manual-times').addEventListener('click', function() {
                const cityName = document.getElementById('city-select').value;
                
                // حذف التعديلات اليدوية للمدينة الحالية
                if (PrayerManager.manualAdjustments[cityName]) {
                    delete PrayerManager.manualAdjustments[cityName];
                    PrayerManager.saveManualAdjustments();
                }
                
                // إعادة حساب المواقيت
                PrayerManager.calculatePrayerTimes(cityName);
                
                // تحديث العرض
                displayPrayerTimes();
                displayNextPrayer();
                
                alert('تم إعادة ضبط المواقيت بنجاح');
            });
        }
    </script>
</body>
</html>
