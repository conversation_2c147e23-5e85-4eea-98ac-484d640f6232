/**
 * نظام التعتيم بعنصر واحد فقط
 * يقوم بتشغيل الأذان وتعتيم الشاشة وعرض العد التنازلي للإقامة
 */

// نظام التعتيم بعنصر واحد
const SingleElementDarknessSystem = {
    // المتغيرات العامة
    isActive: false,
    currentPrayer: null,
    countdownInterval: null,
    darknessInterval: null,
    darknessTimeout: null,
    iqamaMinutes: 10, // مدة الإقامة الافتراضية (10 دقائق)

    // طباعة قيمة المتغيرات للتصحيح
    debugVariables: function() {
        console.log('==== متغيرات نظام التعتيم ====');
        console.log(`isActive: ${this.isActive}`);
        console.log(`currentPrayer: ${this.currentPrayer}`);
        console.log(`iqamaMinutes: ${this.iqamaMinutes}`);

        // طباعة مدة التعتيم من PrayerManager
        if (typeof PrayerManager !== 'undefined' && PrayerManager.darknessDurations) {
            console.log('مدة التعتيم المحفوظة:');
            for (const prayer in PrayerManager.darknessDurations) {
                console.log(`${prayer}: ${PrayerManager.darknessDurations[prayer]} دقيقة`);
            }
        }

        console.log('============================');
    },

    // اختبار مدة التعتيم
    testDarknessDuration: function(prayerName) {
        console.log(`اختبار مدة التعتيم لصلاة ${prayerName}...`);

        // قراءة مدة التعتيم من حقل الإدخال
        const darknessDurationInput = document.getElementById(`darkness-duration-${prayerName}`);
        if (darknessDurationInput) {
            const inputValue = darknessDurationInput.value.trim();
            const minutes = parseInt(inputValue);

            if (!isNaN(minutes) && minutes > 0) {
                console.log(`قيمة حقل الإدخال: ${minutes} دقيقة`);
            } else {
                console.error(`قيمة غير صالحة في حقل الإدخال: ${inputValue}`);
            }
        } else {
            console.error(`لم يتم العثور على حقل مدة التعتيم لصلاة ${prayerName}`);
        }

        // قراءة مدة التعتيم من PrayerManager
        if (typeof PrayerManager !== 'undefined' && PrayerManager.darknessDurations) {
            const savedDuration = PrayerManager.darknessDurations[prayerName];
            if (savedDuration) {
                console.log(`مدة التعتيم المحفوظة في PrayerManager: ${savedDuration} دقيقة`);
            } else {
                console.log(`لم يتم العثور على مدة تعتيم محفوظة لصلاة ${prayerName} في PrayerManager`);
            }
        } else {
            console.log('لم يتم العثور على كائن PrayerManager.darknessDurations');
        }

        // قراءة مدة التعتيم من التخزين المحلي
        try {
            const savedDurations = localStorage.getItem('darknessDurations');
            if (savedDurations) {
                const durations = JSON.parse(savedDurations);
                if (durations[prayerName]) {
                    console.log(`مدة التعتيم المحفوظة في التخزين المحلي: ${durations[prayerName]} دقيقة`);
                } else {
                    console.log(`لم يتم العثور على مدة تعتيم محفوظة لصلاة ${prayerName} في التخزين المحلي`);
                }
            } else {
                console.log('لم يتم العثور على مدة تعتيم محفوظة في التخزين المحلي');
            }
        } catch (error) {
            console.error('خطأ في قراءة مدة التعتيم من التخزين المحلي:', error);
        }
    },

    // الحصول على الاسم العربي للصلاة
    getPrayerArabicName: function(prayerName) {
        const prayerNames = {
            'fajr': 'الفجر',
            'dhuhr': 'الظهر',
            'asr': 'العصر',
            'maghrib': 'المغرب',
            'isha': 'العشاء'
        };

        return prayerNames[prayerName] || prayerName;
    },

    // تهيئة النظام
    initialize: function() {
        console.log('تهيئة نظام التعتيم بعنصر واحد...');

        // استرجاع مدة الإقامة المحفوظة
        this.loadSavedIqamaMinutes();
        console.log(`مدة الإقامة المحفوظة: ${this.iqamaMinutes} دقيقة`);

        // استرجاع مدة التعتيم المحفوظة
        this.loadSavedDarknessDurations();

        // إنشاء عنصر التعتيم
        this.createDarknessElement();

        // إعداد مراقبة وقت الصلاة
        this.setupPrayerTimeMonitoring();

        // تحديث حقل مدة الإقامة إذا كان موجودًا
        this.updateIqamaInputField();

        // تحديث حقول مدة التعتيم إذا كانت موجودة
        this.updateDarknessDurationInputFields();

        // إضافة مستمع حدث لتحديث مدة الإقامة عند تغيير القيمة
        const iqamaInput = document.getElementById('iqama-minutes');
        if (iqamaInput) {
            iqamaInput.addEventListener('input', () => {
                console.log(`تغيير قيمة حقل الإدخال: ${iqamaInput.value}`);
            });
        }

        console.log('تم تهيئة نظام التعتيم بعنصر واحد بنجاح');
    },

    // استرجاع مدة التعتيم المحفوظة
    loadSavedDarknessDurations: function() {
        try {
            // قائمة الصلوات
            const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];

            // استرجاع مدة التعتيم لكل صلاة
            for (const prayer of prayers) {
                const localStorageKey = `darknessDuration_${prayer}`;
                const savedDuration = localStorage.getItem(localStorageKey);

                if (savedDuration) {
                    const minutes = parseInt(savedDuration);
                    if (!isNaN(minutes) && minutes > 0) {
                        // حفظ القيمة في PrayerManager
                        if (typeof PrayerManager !== 'undefined') {
                            if (!PrayerManager.darknessDurations) {
                                PrayerManager.darknessDurations = {};
                            }
                            PrayerManager.darknessDurations[prayer] = minutes;
                        }

                        console.log(`تم استرجاع مدة التعتيم لصلاة ${prayer}: ${minutes} دقيقة`);
                    }
                }
            }

            // حفظ القيم في PrayerManager
            if (typeof PrayerManager !== 'undefined' && typeof PrayerManager.saveDarknessDurations === 'function') {
                PrayerManager.saveDarknessDurations();
                console.log('تم حفظ مدة التعتيم في PrayerManager');
            }
        } catch (error) {
            console.error('خطأ في استرجاع مدة التعتيم:', error);
        }
    },

    // تحديث حقول مدة التعتيم
    updateDarknessDurationInputFields: function() {
        try {
            // قائمة الصلوات
            const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];

            // تحديث حقل مدة التعتيم لكل صلاة
            for (const prayer of prayers) {
                const darknessDurationInput = document.getElementById(`darkness-duration-${prayer}`);
                if (darknessDurationInput) {
                    // محاولة قراءة مدة التعتيم من التخزين المحلي
                    const localStorageKey = `darknessDuration_${prayer}`;
                    const savedDuration = localStorage.getItem(localStorageKey);

                    if (savedDuration) {
                        const minutes = parseInt(savedDuration);
                        if (!isNaN(minutes) && minutes > 0) {
                            darknessDurationInput.value = minutes;
                            console.log(`تم تحديث حقل مدة التعتيم لصلاة ${prayer}: ${minutes} دقيقة`);
                        }
                    } else if (typeof PrayerManager !== 'undefined' && PrayerManager.darknessDurations && PrayerManager.darknessDurations[prayer]) {
                        // محاولة قراءة مدة التعتيم من PrayerManager
                        const minutes = PrayerManager.darknessDurations[prayer];
                        if (!isNaN(minutes) && minutes > 0) {
                            darknessDurationInput.value = minutes;
                            console.log(`تم تحديث حقل مدة التعتيم لصلاة ${prayer} من PrayerManager: ${minutes} دقيقة`);

                            // حفظ القيمة في التخزين المحلي
                            localStorage.setItem(localStorageKey, minutes.toString());
                        }
                    }
                }
            }
        } catch (error) {
            console.error('خطأ في تحديث حقول مدة التعتيم:', error);
        }
    },

    // استرجاع مدة الإقامة المحفوظة
    loadSavedIqamaMinutes: function() {
        try {
            // محاولة استرجاع مدة الإقامة من التخزين المحلي
            const savedIqamaMinutes = localStorage.getItem('iqamaMinutes');
            if (savedIqamaMinutes) {
                const minutes = parseInt(savedIqamaMinutes);
                if (!isNaN(minutes) && minutes > 0) {
                    this.iqamaMinutes = minutes;
                    console.log(`تم استرجاع مدة الإقامة من التخزين المحلي: ${this.iqamaMinutes} دقيقة`);

                    // تحديث المتغير العام
                    window.iqamaDuration = minutes;
                    console.log(`تم تحديث المتغير العام iqamaDuration: ${minutes} دقيقة`);

                    // تحديث حقل الإدخال إذا كان موجودًا
                    const iqamaInput = document.getElementById('iqama-minutes');
                    if (iqamaInput) {
                        iqamaInput.value = this.iqamaMinutes;
                        console.log(`تم تحديث حقل الإدخال بالقيمة: ${this.iqamaMinutes} دقيقة`);
                    }
                }
            } else {
                // محاولة استرجاع مدة الإقامة من PrayerManager
                if (typeof PrayerManager !== 'undefined' && PrayerManager.iqamaSettings && PrayerManager.iqamaSettings.defaultDuration) {
                    const minutes = PrayerManager.iqamaSettings.defaultDuration;
                    if (!isNaN(minutes) && minutes > 0) {
                        this.iqamaMinutes = minutes;
                        console.log(`تم استرجاع مدة الإقامة من PrayerManager: ${this.iqamaMinutes} دقيقة`);

                        // تحديث المتغير العام
                        window.iqamaDuration = minutes;
                        console.log(`تم تحديث المتغير العام iqamaDuration: ${minutes} دقيقة`);

                        // حفظ القيمة في التخزين المحلي
                        localStorage.setItem('iqamaMinutes', this.iqamaMinutes.toString());
                        console.log(`تم حفظ مدة الإقامة في التخزين المحلي: ${this.iqamaMinutes} دقيقة`);

                        // تحديث حقل الإدخال إذا كان موجودًا
                        const iqamaInput = document.getElementById('iqama-minutes');
                        if (iqamaInput) {
                            iqamaInput.value = this.iqamaMinutes;
                            console.log(`تم تحديث حقل الإدخال بالقيمة: ${this.iqamaMinutes} دقيقة`);
                        }
                    }
                }
            }

            // طباعة قيمة المتغيرات للتصحيح
            this.debugVariables();
        } catch (error) {
            console.error('خطأ في استرجاع مدة الإقامة:', error);
        }
    },

    // تحديث حقل مدة الإقامة
    updateIqamaInputField: function() {
        const iqamaInput = document.getElementById('iqama-minutes');
        if (iqamaInput) {
            iqamaInput.value = this.iqamaMinutes;
            console.log(`تم تحديث حقل مدة الإقامة: ${this.iqamaMinutes} دقيقة`);

            // إضافة مستمع الحدث لحفظ القيمة عند تغييرها
            if (!iqamaInput.hasEventListener) {
                iqamaInput.addEventListener('change', () => {
                    this.saveIqamaMinutes(parseInt(iqamaInput.value) || 10);
                });
                iqamaInput.hasEventListener = true;
            }
        }
    },

    // حفظ مدة الإقامة
    saveIqamaMinutes: function(minutes) {
        console.log(`محاولة حفظ مدة الإقامة: ${minutes} دقيقة (القيمة السابقة: ${this.iqamaMinutes} دقيقة)`);

        // التأكد من أن القيمة صالحة
        if (isNaN(minutes) || minutes <= 0) {
            console.error(`قيمة غير صالحة لمدة الإقامة: ${minutes}`);
            return;
        }

        // التأكد من أن القيمة ضمن الحدود المسموح بها (1-40 دقيقة)
        if (minutes > 40) {
            console.warn(`تم تحديد قيمة كبيرة لمدة الإقامة: ${minutes} دقيقة، سيتم تحديدها إلى 40 دقيقة`);
            minutes = 40;
        } else if (minutes < 1) {
            console.warn(`تم تحديد قيمة صغيرة لمدة الإقامة: ${minutes} دقيقة، سيتم تحديدها إلى 1 دقيقة`);
            minutes = 1;
        }

        // تحديث المتغير
        this.iqamaMinutes = minutes;

        // تحديث المتغير العام
        window.iqamaDuration = minutes;
        console.log(`تم تحديث المتغير العام iqamaDuration: ${minutes} دقيقة`);

        // حفظ مدة الإقامة في التخزين المحلي
        try {
            localStorage.setItem('iqamaMinutes', this.iqamaMinutes.toString());
            console.log(`تم حفظ مدة الإقامة في التخزين المحلي: ${this.iqamaMinutes} دقيقة`);

            // تحديث حقل الإدخال إذا كان موجودًا
            const iqamaInput = document.getElementById('iqama-minutes');
            if (iqamaInput) {
                iqamaInput.value = this.iqamaMinutes;
                console.log(`تم تحديث حقل الإدخال بالقيمة: ${this.iqamaMinutes} دقيقة`);
            }

            // تحديث جميع الإعدادات المتعلقة بمدة الإقامة
            this.updateAllIqamaSettings();

            // عرض رسالة تأكيد
            alert(`تم حفظ مدة الإقامة: ${this.iqamaMinutes} دقيقة`);
        } catch (error) {
            console.error('خطأ في حفظ مدة الإقامة:', error);
        }
    },

    // تحديث جميع الإعدادات المتعلقة بمدة الإقامة
    updateAllIqamaSettings: function() {
        // تحديث الإعدادات في PrayerManager إذا كان موجودًا
        if (typeof PrayerManager !== 'undefined') {
            if (!PrayerManager.iqamaSettings) {
                PrayerManager.iqamaSettings = {};
            }

            PrayerManager.iqamaSettings.defaultDuration = this.iqamaMinutes;

            // حفظ الإعدادات
            if (typeof PrayerManager.saveSettings === 'function') {
                PrayerManager.saveSettings();
                console.log(`تم تحديث إعدادات الإقامة في PrayerManager: ${this.iqamaMinutes} دقيقة`);
            }
        }

        // تحديث أي متغيرات أخرى متعلقة بمدة الإقامة
        if (typeof window.iqamaDuration !== 'undefined') {
            window.iqamaDuration = this.iqamaMinutes;
            console.log(`تم تحديث متغير iqamaDuration العالمي: ${this.iqamaMinutes} دقيقة`);
        }

        // تحديث أي عناصر HTML أخرى متعلقة بمدة الإقامة
        const iqamaElements = document.querySelectorAll('[data-iqama-duration]');
        iqamaElements.forEach(element => {
            element.setAttribute('data-iqama-duration', this.iqamaMinutes);
            console.log(`تم تحديث عنصر HTML بسمة data-iqama-duration: ${this.iqamaMinutes} دقيقة`);
        });
    },

    // إنشاء عنصر التعتيم
    createDarknessElement: function() {
        // إنشاء عنصر التعتيم
        const overlay = document.createElement('div');
        overlay.id = 'single-darkness-overlay';
        overlay.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background-color:rgba(0,0,0,0.9);z-index:9999;display:none;color:white;font-family:Arial,sans-serif;text-align:center;';

        // إنشاء عداد مدة التعتيم في أعلى يمين الشاشة
        const darknessTimer = document.createElement('div');
        darknessTimer.id = 'darkness-timer-display';
        darknessTimer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 16px;
            font-family: Arial, sans-serif;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            z-index: 10000;
            display: none;
            direction: rtl;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
        `;
        darknessTimer.textContent = 'مدة التعتيم: 10:00';

        // إضافة عنصر التعتيم إلى الصفحة
        document.body.appendChild(overlay);
        document.body.appendChild(darknessTimer);

        // إضافة مستمع النقر لإغلاق التعتيم
        overlay.addEventListener('click', () => {
            this.stopDarkness();
        });

        // حفظ مرجع لعنصر التعتيم وعداد التعتيم
        this.overlay = overlay;
        this.darknessTimer = darknessTimer;
    },

    // إعداد مراقبة وقت الصلاة
    setupPrayerTimeMonitoring: function() {
        // التحقق من وقت الصلاة كل دقيقة
        setInterval(() => {
            this.checkPrayerTime();
        }, 60 * 1000); // كل دقيقة

        // التحقق الأولي
        this.checkPrayerTime();
    },

    // التحقق من وقت الصلاة
    checkPrayerTime: function() {
        // التحقق مما إذا كان التعتيم مفعلاً في الإعدادات
        if (!PrayerManager.settings.darknessEnabled) {
            return;
        }

        // الحصول على الصلاة القادمة
        const nextPrayer = window.getNextPrayer ? window.getNextPrayer() : PrayerManager.getNextPrayer();
        if (!nextPrayer) {
            return;
        }

        // الحصول على الوقت الحالي
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();

        // تحويل وقت الصلاة إلى ساعات ودقائق
        let prayerHour, prayerMinute;

        if (nextPrayer.time.includes('ص') || nextPrayer.time.includes('م')) {
            // إذا كان الوقت بنظام 12 ساعة
            const isPM = nextPrayer.time.includes('م');
            const timeParts = nextPrayer.time.replace(/[صم]/g, '').trim().split(':');
            prayerHour = parseInt(timeParts[0]);
            prayerMinute = parseInt(timeParts[1]);

            // تحويل إلى نظام 24 ساعة
            if (isPM && prayerHour < 12) prayerHour += 12;
            if (!isPM && prayerHour === 12) prayerHour = 0;
        } else {
            // إذا كان الوقت بنظام 24 ساعة
            const timeParts = nextPrayer.time.split(':');
            prayerHour = parseInt(timeParts[0]);
            prayerMinute = parseInt(timeParts[1]);
        }

        // التحقق مما إذا كان الوقت الحالي هو وقت الصلاة
        if (currentHour === prayerHour && currentMinute === prayerMinute) {
            // تجنب تكرار التعتيم لنفس الصلاة
            if (this.currentPrayer !== nextPrayer.name) {
                this.currentPrayer = nextPrayer.name;

                // تحديث مدة الإقامة من الحقل قبل بدء العد التنازلي
                const iqamaInput = document.getElementById('iqama-minutes');
                if (iqamaInput) {
                    const minutes = parseInt(iqamaInput.value) || 10;
                    console.log(`تحديث مدة الإقامة من الحقل: ${minutes} دقيقة`);
                    this.saveIqamaMinutes(minutes);
                }

                // تشغيل الأذان إذا كان مفعلاً
                if (PrayerManager.settings.adhanEnabled) {
                    this.playAdhan();
                }

                // بدء العد التنازلي للإقامة
                this.startIqamaCountdown(nextPrayer);
            }
        }
    },

    // تشغيل الأذان
    playAdhan: function() {
        try {
            const adhanAudio = document.getElementById('adhan-audio');
            if (adhanAudio) {
                // تعيين مصدر الأذان
                adhanAudio.src = 'audio/adhan.mp3';

                // محاولة تشغيل الأذان
                const playPromise = adhanAudio.play();

                // التعامل مع الوعد
                if (playPromise !== undefined) {
                    playPromise.then(_ => {
                        console.log('تم تشغيل الأذان بنجاح');
                    }).catch(error => {
                        console.error('خطأ في تشغيل الأذان:', error);
                        // محاولة تشغيل الأذان بعد تفاعل المستخدم
                        alert('انقر موافق لتشغيل الأذان');
                        adhanAudio.play().catch(e => console.error('فشل تشغيل الأذان مرة أخرى:', e));
                    });
                }
            } else {
                console.error('لم يتم العثور على عنصر الأذان');
            }
        } catch (error) {
            console.error('خطأ في تشغيل الأذان:', error);
        }
    },

    // بدء العد التنازلي للإقامة
    startIqamaCountdown: function(prayer) {
        console.log('بدء العد التنازلي للإقامة...');

        // قراءة مدة الإقامة من المتغير العام أولاً
        if (typeof window.iqamaDuration !== 'undefined' && window.iqamaDuration > 0) {
            this.iqamaMinutes = window.iqamaDuration;
            console.log(`تم استخدام مدة الإقامة من المتغير العام: ${this.iqamaMinutes} دقيقة`);
        }

        // ثم قراءة مدة الإقامة من حقل الإدخال مباشرة
        const iqamaInput = document.getElementById('iqama-minutes');
        if (iqamaInput) {
            const inputValue = iqamaInput.value.trim();
            const minutes = parseInt(inputValue);

            if (!isNaN(minutes) && minutes > 0) {
                // التأكد من أن القيمة ضمن الحدود المسموح بها (1-40 دقيقة)
                let validMinutes = minutes;
                if (validMinutes > 40) {
                    console.warn(`تم تحديد قيمة كبيرة لمدة الإقامة: ${validMinutes} دقيقة، سيتم تحديدها إلى 40 دقيقة`);
                    validMinutes = 40;
                    // تحديث حقل الإدخال
                    iqamaInput.value = validMinutes;
                } else if (validMinutes < 1) {
                    console.warn(`تم تحديد قيمة صغيرة لمدة الإقامة: ${validMinutes} دقيقة، سيتم تحديدها إلى 1 دقيقة`);
                    validMinutes = 1;
                    // تحديث حقل الإدخال
                    iqamaInput.value = validMinutes;
                }

                // تحديث مدة الإقامة بالقيمة المدخلة
                this.iqamaMinutes = validMinutes;
                console.log(`تم تحديث مدة الإقامة من حقل الإدخال: ${this.iqamaMinutes} دقيقة`);

                // حفظ القيمة في التخزين المحلي
                localStorage.setItem('iqamaMinutes', this.iqamaMinutes.toString());

                // تحديث المتغير العام
                window.iqamaDuration = validMinutes;
                console.log(`تم تحديث المتغير العام iqamaDuration: ${validMinutes} دقيقة`);
            }
        }

        // طباعة قيمة المتغيرات للتصحيح
        this.debugVariables();

        // عرض رسالة تأكيد
        alert(`سيبدأ العد التنازلي للإقامة لمدة ${this.iqamaMinutes} دقيقة`);

        // إظهار عنصر التعتيم
        this.overlay.style.display = 'flex';
        this.overlay.style.flexDirection = 'column';
        this.overlay.style.justifyContent = 'center';
        this.overlay.style.alignItems = 'center';
        this.isActive = true;

        // تحديث محتوى عنصر التعتيم
        this.updateOverlayContent(prayer, `صلاة ${prayer.arabicName}`, `الإقامة بعد ${this.iqamaMinutes}:00 دقيقة`);

        // التأكد من أن مدة الإقامة ضمن الحدود المسموح بها
        if (this.iqamaMinutes > 40) {
            console.warn(`تم تحديد قيمة كبيرة لمدة الإقامة: ${this.iqamaMinutes} دقيقة، سيتم تحديدها إلى 40 دقيقة`);
            this.iqamaMinutes = 40;

            // تحديث المتغير العام
            window.iqamaDuration = this.iqamaMinutes;

            // تحديث حقل الإدخال إذا كان موجودًا
            const iqamaInput = document.getElementById('iqama-minutes');
            if (iqamaInput) {
                iqamaInput.value = this.iqamaMinutes;
            }

            // حفظ القيمة في التخزين المحلي
            localStorage.setItem('iqamaMinutes', this.iqamaMinutes.toString());
        }

        // بدء العد التنازلي باستخدام القيمة المحدثة
        let remainingSeconds = this.iqamaMinutes * 60;
        console.log(`بدء العد التنازلي لمدة ${this.iqamaMinutes} دقيقة (${remainingSeconds} ثانية)`);

        // مسح العد التنازلي السابق إذا كان موجودًا
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
        }

        // بدء العد التنازلي الجديد
        this.countdownInterval = setInterval(() => {
            remainingSeconds--;

            if (remainingSeconds <= 0) {
                // إيقاف العد التنازلي
                clearInterval(this.countdownInterval);
                console.log('انتهى العد التنازلي للإقامة');

                // بدء التعتيم
                this.startDarkness(prayer);
            } else {
                // تحديث عرض العد التنازلي
                const minutes = Math.floor(remainingSeconds / 60);
                const seconds = remainingSeconds % 60;
                const countdownText = `الإقامة بعد ${minutes}:${seconds < 10 ? '0' + seconds : seconds} دقيقة`;
                this.updateCountdownText(countdownText);

                // طباعة قيمة العد التنازلي كل 10 ثوانٍ
                if (seconds % 10 === 0) {
                    console.log(`العد التنازلي: ${countdownText}`);
                }
            }
        }, 1000);
    },

    // بدء التعتيم
    startDarkness: function(prayer) {
        console.log(`بدء التعتيم لصلاة ${prayer.arabicName}...`);

        // تحديد مدة التعتيم
        let darknessDuration = 10; // القيمة الافتراضية

        try {
            // قراءة مدة التعتيم من حقل الإدخال مباشرة (هذا هو الأهم)
            const darknessDurationInput = document.getElementById(`darkness-duration-${prayer.name}`);
            if (darknessDurationInput) {
                const inputValue = darknessDurationInput.value.trim();
                const minutes = parseInt(inputValue);

                if (!isNaN(minutes) && minutes > 0) {
                    darknessDuration = minutes;
                    console.log(`تم قراءة مدة التعتيم من حقل الإدخال: ${darknessDuration} دقيقة`);

                    // حفظ القيمة في التخزين المحلي
                    const localStorageKey = `darknessDuration_${prayer.name}`;
                    localStorage.setItem(localStorageKey, darknessDuration.toString());
                    console.log(`تم حفظ مدة التعتيم في التخزين المحلي: ${darknessDuration} دقيقة`);

                    // حفظ القيمة في PrayerManager
                    if (typeof PrayerManager !== 'undefined') {
                        if (!PrayerManager.darknessDurations) {
                            PrayerManager.darknessDurations = {};
                        }
                        PrayerManager.darknessDurations[prayer.name] = darknessDuration;
                        if (typeof PrayerManager.saveDarknessDurations === 'function') {
                            PrayerManager.saveDarknessDurations();
                            console.log(`تم حفظ مدة التعتيم في PrayerManager: ${darknessDuration} دقيقة`);
                        }
                    }
                }
            } else {
                console.log(`لم يتم العثور على حقل مدة التعتيم لصلاة ${prayer.name}`);

                // محاولة قراءة مدة التعتيم من التخزين المحلي
                const localStorageKey = `darknessDuration_${prayer.name}`;
                const savedDurationFromLocalStorage = localStorage.getItem(localStorageKey);

                if (savedDurationFromLocalStorage) {
                    const minutes = parseInt(savedDurationFromLocalStorage);
                    if (!isNaN(minutes) && minutes > 0) {
                        darknessDuration = minutes;
                        console.log(`تم قراءة مدة التعتيم من التخزين المحلي: ${darknessDuration} دقيقة`);
                    }
                } else if (typeof PrayerManager !== 'undefined' && PrayerManager.darknessDurations && PrayerManager.darknessDurations[prayer.name]) {
                    // محاولة قراءة مدة التعتيم من PrayerManager
                    const savedDuration = PrayerManager.darknessDurations[prayer.name];
                    if (!isNaN(savedDuration) && savedDuration > 0) {
                        darknessDuration = savedDuration;
                        console.log(`تم قراءة مدة التعتيم من PrayerManager: ${darknessDuration} دقيقة`);
                    }
                }
            }

            // طباعة قيمة المتغيرات للتصحيح
            console.log(`مدة التعتيم النهائية: ${darknessDuration} دقيقة`);

            // عرض رسالة تأكيد
            alert(`سيبدأ التعتيم لمدة ${darknessDuration} دقيقة`);

            // تحديث نص العد التنازلي
            this.updateCountdownText(`مدة التعتيم المتبقية: ${darknessDuration}:00 دقيقة`);

            // تحديث محتوى عنصر التعتيم
            this.updateOverlayContent(prayer, `صلاة ${prayer.arabicName}`, `مدة التعتيم المتبقية: ${darknessDuration}:00 دقيقة`);

            // تخزين مدة التعتيم الحالية في متغير عام
            this.currentDarknessDuration = darknessDuration;

            // بدء العد التنازلي لمدة التعتيم
            let remainingSeconds = darknessDuration * 60;
            console.log(`بدء العد التنازلي للتعتيم لمدة ${darknessDuration} دقيقة (${remainingSeconds} ثانية)`);

            // تخزين مدة التعتيم في متغير عام للاستخدام لاحقًا
            window.currentDarknessDuration = darknessDuration;
        } catch (error) {
            console.error('خطأ في بدء التعتيم:', error);
            // استخدام القيمة الافتراضية في حالة حدوث خطأ
            darknessDuration = 10;
            this.currentDarknessDuration = darknessDuration;
            window.currentDarknessDuration = darknessDuration;
            console.log(`استخدام مدة التعتيم الافتراضية: ${darknessDuration} دقيقة`);
        }

        try {
            // مسح العد التنازلي السابق إذا كان موجودًا
            if (this.darknessInterval) {
                clearInterval(this.darknessInterval);
                this.darknessInterval = null;
                console.log('تم مسح العد التنازلي السابق للتعتيم');
            }

            // مسح مؤقت التعتيم السابق إذا كان موجودًا
            if (this.darknessTimeout) {
                clearTimeout(this.darknessTimeout);
                this.darknessTimeout = null;
                console.log('تم مسح مؤقت التعتيم السابق');
            }

            // استخدام مدة التعتيم المحفوظة في المتغير العام
            const finalDarknessDuration = window.currentDarknessDuration || this.currentDarknessDuration || darknessDuration;
            console.log(`مدة التعتيم النهائية المستخدمة: ${finalDarknessDuration} دقيقة`);

            // حساب الثواني المتبقية
            let remainingSeconds = finalDarknessDuration * 60;
            console.log(`بدء العد التنازلي للتعتيم: ${remainingSeconds} ثانية`);

            // إظهار عداد التعتيم في أعلى يمين الشاشة
            if (this.darknessTimer) {
                this.darknessTimer.style.display = 'block';
                this.darknessTimer.textContent = `مدة التعتيم: ${finalDarknessDuration}:00`;
            }

            // عرض رسالة تأكيد
            alert(`بدء العد التنازلي للتعتيم لمدة ${finalDarknessDuration} دقيقة`);

            // بدء العد التنازلي الجديد
            this.darknessInterval = setInterval(() => {
                remainingSeconds--;

                if (remainingSeconds <= 0) {
                    // إيقاف العد التنازلي
                    clearInterval(this.darknessInterval);
                    this.darknessInterval = null;
                    console.log('انتهى العد التنازلي للتعتيم');

                    // إيقاف التعتيم
                    this.stopDarkness();
                } else {
                    // تحديث عرض العد التنازلي
                    const minutes = Math.floor(remainingSeconds / 60);
                    const seconds = remainingSeconds % 60;
                    const countdownText = `مدة التعتيم المتبقية: ${minutes}:${seconds < 10 ? '0' + seconds : seconds} دقيقة`;
                    this.updateCountdownText(countdownText);

                    // تحديث عداد التعتيم في أعلى يمين الشاشة
                    if (this.darknessTimer) {
                        this.darknessTimer.textContent = `مدة التعتيم: ${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
                    }

                    // تحديث محتوى عنصر التعتيم
                    this.updateOverlayContent(prayer, `صلاة ${prayer.arabicName}`, countdownText);

                    // طباعة قيمة العد التنازلي كل 10 ثوانٍ
                    if (seconds % 10 === 0) {
                        console.log(`العد التنازلي للتعتيم: ${countdownText}`);
                    }
                }
            }, 1000);

            // بدء مؤقت التعتيم الجديد (كاحتياط)
            this.darknessTimeout = setTimeout(() => {
                console.log('انتهى مؤقت التعتيم (احتياطي)');
                // إيقاف التعتيم
                this.stopDarkness();
            }, finalDarknessDuration * 60 * 1000 + 5000); // تحويل الدقائق إلى مللي ثانية + 5 ثوانٍ إضافية

            console.log(`تم بدء التعتيم لمدة ${finalDarknessDuration} دقيقة`);
        } catch (error) {
            console.error('خطأ في بدء العد التنازلي للتعتيم:', error);

            // محاولة استخدام القيمة الافتراضية في حالة حدوث خطأ
            try {
                const defaultDuration = 10;
                console.log(`استخدام مدة التعتيم الافتراضية: ${defaultDuration} دقيقة`);

                // بدء العد التنازلي باستخدام القيمة الافتراضية
                let remainingSeconds = defaultDuration * 60;

                this.darknessInterval = setInterval(() => {
                    remainingSeconds--;

                    if (remainingSeconds <= 0) {
                        clearInterval(this.darknessInterval);
                        this.stopDarkness();
                    } else {
                        const minutes = Math.floor(remainingSeconds / 60);
                        const seconds = remainingSeconds % 60;
                        const countdownText = `مدة التعتيم المتبقية: ${minutes}:${seconds < 10 ? '0' + seconds : seconds} دقيقة`;
                        this.updateCountdownText(countdownText);

                        // تحديث عداد التعتيم في أعلى يمين الشاشة
                        if (this.darknessTimer) {
                            this.darknessTimer.textContent = `مدة التعتيم: ${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
                        }

                        this.updateOverlayContent(prayer, `صلاة ${prayer.arabicName}`, countdownText);
                    }
                }, 1000);

                this.darknessTimeout = setTimeout(() => {
                    this.stopDarkness();
                }, defaultDuration * 60 * 1000 + 5000);
            } catch (fallbackError) {
                console.error('خطأ في استخدام القيمة الافتراضية للتعتيم:', fallbackError);
                this.stopDarkness(); // إيقاف التعتيم في حالة حدوث خطأ
            }
        }
    },

    // إيقاف التعتيم
    stopDarkness: function() {
        console.log('إيقاف التعتيم...');

        // إخفاء عنصر التعتيم
        this.overlay.style.display = 'none';
        this.isActive = false;
        console.log('تم إخفاء عنصر التعتيم');

        // إخفاء عداد التعتيم
        if (this.darknessTimer) {
            this.darknessTimer.style.display = 'none';
            console.log('تم إخفاء عداد التعتيم');
        }

        // مسح العد التنازلي للإقامة
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
            console.log('تم مسح العد التنازلي للإقامة');
        }

        // مسح العد التنازلي للتعتيم
        if (this.darknessInterval) {
            clearInterval(this.darknessInterval);
            this.darknessInterval = null;
            console.log('تم مسح العد التنازلي للتعتيم');
        }

        // مسح مؤقت التعتيم
        if (this.darknessTimeout) {
            clearTimeout(this.darknessTimeout);
            this.darknessTimeout = null;
            console.log('تم مسح مؤقت التعتيم');
        }

        // إيقاف الأذان إذا كان يعمل
        try {
            const adhanAudio = document.getElementById('adhan-audio');
            if (adhanAudio && !adhanAudio.paused) {
                adhanAudio.pause();
                adhanAudio.currentTime = 0;
                console.log('تم إيقاف الأذان');
            }
        } catch (error) {
            console.error('خطأ في إيقاف الأذان:', error);
        }

        console.log('تم إيقاف التعتيم بنجاح');
    },

    // تحديث محتوى عنصر التعتيم
    updateOverlayContent: function(prayer, title, countdownText) {
        // الحصول على الوقت الحالي
        const now = new Date();
        const hours = now.getHours();
        const minutes = now.getMinutes();
        const seconds = now.getSeconds();

        // تنسيق الوقت - استخدام نظام 12 ساعة دائماً أثناء التعتيم
        let timeString;
        // نظام 12 ساعة
        const isPM = hours >= 12;
        let hours12 = hours % 12;
        if (hours12 === 0) hours12 = 12;

        timeString = `${hours12}:${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds} ${isPM ? 'م' : 'ص'}`;

        // تحديث محتوى عنصر التعتيم
        this.overlay.innerHTML = `
            <div style="font-size:2rem;margin:0 0 20px 0;padding:0;line-height:1;">${title}</div>
            <div id="single-countdown-text" style="font-size:1.5rem;margin:0 0 20px 0;padding:0;line-height:1;">${countdownText}</div>
            <div style="font-size:3rem;margin:0;padding:0;line-height:1;">${timeString}</div>
        `;

        // تحديث الساعة كل ثانية
        setTimeout(() => {
            if (this.isActive) {
                this.updateOverlayContent(prayer, title, document.getElementById('single-countdown-text').textContent);
            }
        }, 1000);
    },

    // تحديث نص العد التنازلي
    updateCountdownText: function(text) {
        const countdownElement = document.getElementById('single-countdown-text');
        if (countdownElement) {
            countdownElement.textContent = text;
        }
    }
};

// تصدير الكائن للاستخدام
window.SingleElementDarknessSystem = SingleElementDarknessSystem;
