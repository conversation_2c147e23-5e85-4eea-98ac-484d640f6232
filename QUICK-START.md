# دليل البدء السريع - ساعة المسجد

## 🚀 البدء السريع (5 دقائق)

### 1. تشغيل التطبيق محلياً

```bash
# طريقة 1: باستخدام Python
python -m http.server 8000

# طريقة 2: باستخدام Node.js
npx serve . -p 3000

# طريقة 3: باستخدام PHP
php -S localhost:8000

# ثم افتح المتصفح على:
# http://localhost:8000
```

### 2. اختبار التحسينات الجديدة

```bash
# افتح ملف الاختبار
open test-darkness-timer.html
# أو في المتصفح:
# http://localhost:8000/test-darkness-timer.html
```

## 📱 إنشاء تطبيق أندرويد

### الطريقة الأولى: PWA (الأسهل)

1. **افتح الموقع** في Chrome على الهاتف
2. **انقر على القائمة** (⋮)
3. **اختر "إضافة إلى الشاشة الرئيسية"**
4. **انقر "إضافة"**

✅ **مكتمل!** التطبيق الآن على شاشتك الرئيسية

### الطريقة الثانية: Capacitor (تطبيق أصلي)

```bash
# 1. تثبيت Capacitor
npm install @capacitor/core @capacitor/cli @capacitor/android

# 2. تهيئة المشروع
npx cap init "ساعة المسجد" "com.mosque.clock"

# 3. إضافة منصة أندرويد
npx cap add android

# 4. نسخ الملفات
npx cap sync

# 5. فتح في Android Studio
npx cap open android

# 6. بناء التطبيق من Android Studio
```

## 💻 إنشاء تطبيق سطح المكتب

### الطريقة الأولى: PWA (الأسهل)

1. **افتح الموقع** في Chrome أو Edge
2. **انقر على أيقونة التثبيت** في شريط العنوان
3. **انقر "تثبيت"**

✅ **مكتمل!** التطبيق الآن في قائمة التطبيقات

### الطريقة الثانية: Electron

```bash
# 1. إنشاء مجلد المشروع
mkdir mosque-clock-desktop
cd mosque-clock-desktop

# 2. تهيئة npm
npm init -y

# 3. تثبيت Electron
npm install electron --save-dev

# 4. إنشاء ملف main.js
# (راجع desktop-setup.md للكود الكامل)

# 5. تشغيل التطبيق
npm start
```

## 📺 إعداد تطبيق التلفاز

### Android TV

```bash
# 1. استخدام نفس إعداد أندرويد
npx cap add android

# 2. تعديل AndroidManifest.xml
# إضافة دعم Android TV

# 3. بناء التطبيق
npx cap build android

# 4. تثبيت على Android TV
adb install app-release.apk
```

### Smart TV (Web App)

1. **رفع الملفات** على خادم ويب
2. **فتح المتصفح** في التلفاز
3. **الانتقال للموقع**
4. **تفعيل الشاشة الكاملة** (F11)

## 🔧 التخصيص السريع

### تغيير الألوان

```css
/* في index.html - قسم الأنماط */
:root {
    --gold-color: #71d3ee;      /* لون ذهبي */
    --dark-pink: #4a3b3b;       /* لون خلفية */
    --light-gray: #faf5f5;      /* لون فاتح */
}
```

### تغيير مدة التعتيم

```javascript
// في prayer-manager.js
darknessDurations: {
    fajr: 15,    // 15 دقيقة للفجر
    dhuhr: 10,   // 10 دقائق للظهر
    asr: 10,     // 10 دقائق للعصر
    maghrib: 5,  // 5 دقائق للمغرب
    isha: 10     // 10 دقائق للعشاء
}
```

### تغيير مدة الإقامة

```javascript
// في prayer-darkness-single.js
iqamaMinutes: 12, // 12 دقيقة للإقامة
```

## 🎯 الميزات الجديدة

### ✅ عداد التعتيم
- يظهر في أعلى يمين الشاشة
- يعد تنازلياً من مدة التعتيم
- خط أبيض صغير مع خلفية شفافة

### ✅ نظام 12 ساعة
- الساعة تظهر بنظام 12 ساعة أثناء التعتيم
- تنسيق: `HH:MM:SS ص/م`
- يعمل تلقائياً بدون إعدادات

### ✅ الاستجابة الكاملة
- **الهاتف**: تخطيط عمودي
- **الجهاز اللوحي**: تخطيط متوسط
- **سطح المكتب**: تخطيط أفقي كامل
- **التلفاز**: خطوط كبيرة وتنقل بالريموت

## 🧪 الاختبار

### اختبار المنصات

```bash
# 1. اختبار الهاتف
# - غير حجم المتصفح إلى 375x667
# - اختبر الاتجاه العمودي والأفقي

# 2. اختبار الجهاز اللوحي
# - غير حجم المتصفح إلى 768x1024
# - اختبر الاتجاهين

# 3. اختبار سطح المكتب
# - استخدم الحجم الكامل
# - اختبر النوافذ المختلفة

# 4. اختبار التلفاز
# - استخدم الشاشة الكاملة (F11)
# - اختبر التنقل بالمفاتيح
```

### اختبار الميزات

```bash
# 1. اختبار عداد التعتيم
open test-darkness-timer.html

# 2. اختبار نظام 12 ساعة
# - ابدأ العد التنازلي
# - راقب تنسيق الساعة

# 3. اختبار الاستجابة
# - غير حجم النافذة
# - اختبر الاتجاهات المختلفة
```

## 📦 النشر

### GitHub Pages

```bash
# 1. رفع الملفات إلى GitHub
git add .
git commit -m "إضافة التحسينات الجديدة"
git push origin main

# 2. تفعيل GitHub Pages
# - اذهب إلى Settings > Pages
# - اختر Source: Deploy from a branch
# - اختر Branch: main
# - انقر Save

# 3. الموقع سيكون متاح على:
# https://username.github.io/repository-name
```

### خادم ويب

```bash
# 1. رفع جميع الملفات إلى الخادم
# 2. التأكد من أن index.html في المجلد الجذر
# 3. تعيين MIME types للملفات:
#    - .json: application/json
#    - .js: application/javascript
#    - .css: text/css
```

## 🆘 حل المشاكل الشائعة

### المشكلة: عداد التعتيم لا يظهر
```bash
# الحل:
# 1. تأكد من تحديث الصفحة (Ctrl+F5)
# 2. تحقق من وحدة تحكم المتصفح للأخطاء
# 3. تأكد من تحميل prayer-darkness-single.js
```

### المشكلة: الساعة لا تظهر بنظام 12 ساعة
```bash
# الحل:
# 1. تأكد من أن التعتيم نشط
# 2. راجع إعدادات المتصفح للوقت
# 3. تحقق من الكود في updateOverlayContent
```

### المشكلة: التطبيق لا يستجيب للاتجاه
```bash
# الحل:
# 1. تأكد من تحميل platform-detector.js
# 2. تأكد من تحميل responsive-handler.js
# 3. تحقق من إعدادات الجهاز للدوران التلقائي
```

## 📞 الدعم

- **الوثائق الكاملة**: README-COMPLETE.md
- **إعداد أندرويد**: android-setup.md
- **إعداد سطح المكتب**: desktop-setup.md
- **التحسينات**: تحسينات-التعتيم.md

---

🕌 **ساعة المسجد v2.0.0** - جاهزة للعمل على جميع المنصات!
