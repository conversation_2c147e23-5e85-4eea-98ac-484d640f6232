<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الساعة الذكية</title>
    <style>
        :root {
            --gold-color: #D4AF37;
            --dark-pink: #4a3b3b;
            --light-gray: #f5f5f5;
            --dark-gray: #333333;
            --text-color: white;
            --text-size: 2em;
            --second-hand-color: #ff0000;
            --minute-hand-color: #000000;
            --hour-hand-color: #8B4513;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background-color: #f0f0f0;
            background-size: cover;
            background-position: center;
            direction: rtl;
            display: flex;
            height: 100vh;
        }

        .vertical-panel {
            width: 5cm;
            height: 100vh;
            background-color: var(--dark-pink);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            box-sizing: border-box;
            position: fixed;
            top: 0;
            right: 0;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.2);
            color: var(--gold-color);
            justify-content: space-between;
            padding-bottom: 20px;
        }

        .settings-btn {
            position: fixed;
            left: 15px;
            top: 15px;
            color: var(--gold-color);
            cursor: pointer;
            font-size: 1.5em;
            background: none;
            border: none;
            z-index: 1000;
        }

        .settings-btn:hover {
            transform: rotate(45deg);
            transition: transform 0.3s;
        }

        .settings-menu {
            display: none;
            position: fixed;
            left: 15px;
            top: 60px;
            width: 200px;
            background: var(--light-gray);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            padding: 10px;
            flex-direction: column;
            gap: 8px;
            color: var(--dark-gray);
            z-index: 999;
            font-size: 0.9em;
        }

        .settings-menu.active {
            display: flex;
        }

        .settings-menu label {
            font-weight: bold;
            font-size: 0.9em;
            margin-bottom: 2px;
        }

        .settings-menu select,
        .settings-menu button {
            width: 100%;
            padding: 6px;
            border-radius: 4px;
            border: 1px solid #ccc;
            font-size: 0.9em;
            box-sizing: border-box;
        }

        button {
            background-color: var(--gold-color);
            color: white;
            border: none;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: bold;
            padding: 6px;
        }

        button:hover {
            background-color: var(--dark-pink);
        }

        .text-overlay {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--text-color);
            font-size: var(--text-size);
            text-align: center;
            z-index: 10;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .analog-clock {
            width: 150px;
            height: 150px;
            border: 6px solid var(--gold-color);
            border-radius: 50%;
            position: relative;
            background: white;
            margin: 20px 0;
        }

        .analog-clock .hand {
            position: absolute;
            bottom: 50%;
            left: 50%;
            transform-origin: bottom;
        }

        .hour-hand {
            width: 4px;
            height: 30%;
            background: var(--hour-hand-color);
        }

        .minute-hand {
            width: 3px;
            height: 40%;
            background: var(--minute-hand-color);
        }

        .second-hand {
            width: 2px;
            height: 45%;
            background: var(--second-hand-color);
        }

        .digital-clock {
            color: var(--gold-color);
            font-size: 1.5em;
            text-align: center;
            margin-top: 10px;
        }

        .dates {
            color: var(--gold-color);
            text-align: center;
            margin-bottom: 20px;
        }

        .gregorian-date,
        .hijri-date {
            margin: 5px 0;
            font-size: 1em;
        }

        .prayer-times {
            width: calc(100% - 5cm);
            background-color: var(--dark-pink);
            color: var(--gold-color);
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
            position: fixed;
            bottom: 0;
            left: 0;
            z-index: 5;
            box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2);
        }

        .prayer-time {
            text-align: center;
        }

        .prayer-name {
            font-size: 1.1em;
            font-weight: bold;
        }

        .prayer-hour {
            font-size: 1em;
            margin-top: 5px;
        }

        .number {
            position: absolute;
            font-size: 12px;
            color: var(--dark-gray);
            text-align: center;
            width: 20px;
            height: 20px;
            line-height: 20px;
            transform: translate(-50%, -50%);
        }

        .countdown-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background-color: white;
            border: 6px solid var(--gold-color);
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px auto;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .countdown-circle.active {
            display: flex;
        }

        .countdown-time {
            font-size: 36px;
            font-weight: bold;
            color: #ff0000;
            text-align: center;
            direction: ltr;
        }

        .countdown-label {
            display: none;
        }

        .prayer-circle {
            display: none;
        }

        .prayer-time-inputs {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin: 10px 0;
        }
        
        .prayer-time-inputs label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
        }
        
        .prayer-time-inputs input[type="time"] {
            width: 100px;
            padding: 4px;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
        
        #savePrayerTimes {
            margin-top: 10px;
            width: 100%;
            background-color: var(--gold-color);
            color: white;
            border: none;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        #savePrayerTimes:hover {
            background-color: var(--dark-pink);
        }
    </style>
</head>
<body>
    <div class="vertical-panel">
        <button class="settings-btn">⚙️</button>
        <div class="settings-menu" id="settingsMenu">
            <h3>الإعدادات</h3>
            <label>
                المنطقة الزمنية:
                <select id="timezone">
                    <option value="+3">+3 (مكة المكرمة)</option>
                    <option value="+2">+2 (القاهرة)</option>
                    <option value="+4">+4 (دبي)</option>
                </select>
            </label>
            <label>
                تنسيق الوقت:
                <select id="timeFormat">
                    <option value="12">12 ساعة</option>
                    <option value="24">24 ساعة</option>
                </select>
            </label>
            <label>
                تشغيل الأذان:
                <input type="checkbox" id="adhanSound" checked>
            </label>
            <h4>تعديل مواقيت الصلوات</h4>
            <div class="prayer-time-inputs">
                <label>
                    الفجر:
                    <input type="time" id="fajrTime" step="60">
                </label>
                <label>
                    الظهر:
                    <input type="time" id="dhuhrTime" step="60">
                </label>
                <label>
                    العصر:
                    <input type="time" id="asrTime" step="60">
                </label>
                <label>
                    المغرب:
                    <input type="time" id="maghribTime" step="60">
                </label>
                <label>
                    العشاء:
                    <input type="time" id="ishaTime" step="60">
                </label>
            </div>
            <button id="savePrayerTimes">حفظ المواقيت</button>
        </div>

        <div class="container">
            <div class="weather-container">
                <div class="weather-display">
                    <img src="" alt="حالة الطقس">
                    <div class="temperature">--°C</div>
                    <div class="description">جاري التحميل...</div>
                </div>
            </div>
            <div class="analog-clock">
                <div class="hand hour-hand"></div>
                <div class="hand minute-hand"></div>
                <div class="hand second-hand"></div>
                <div class="center-dot"></div>
            </div>
            <div class="digital-clock"></div>
        </div>

        <div class="dates">
            <div class="gregorian-date"></div>
            <div class="hijri-date"></div>
        </div>

        <div class="countdown-circle active">
            <div class="countdown-time">00:00:00</div>
            <div class="countdown-label" style="display: none;"></div>
        </div>
    </div>

    <div class="text-overlay" id="text-overlay"></div>

    <div class="prayer-times">
        <div class="prayer-time">
            <div class="prayer-name">الفجر</div>
            <div class="prayer-hour" id="fajr-time"></div>
            <div class="countdown-value" id="fajr-countdown">30:00</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">الشروق</div>
            <div class="prayer-hour" id="sunrise-time"></div>
            <div class="countdown-value" id="sunrise-countdown">00:00</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">الظهر</div>
            <div class="prayer-hour" id="dhuhr-time"></div>
            <div class="countdown-value" id="dhuhr-countdown">15:00</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">العصر</div>
            <div class="prayer-hour" id="asr-time"></div>
            <div class="countdown-value" id="asr-countdown">15:00</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">المغرب</div>
            <div class="prayer-hour" id="maghrib-time"></div>
            <div class="countdown-value" id="maghrib-countdown">09:00</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">العشاء</div>
            <div class="prayer-hour" id="isha-time"></div>
            <div class="countdown-value" id="isha-countdown">15:00</div>
        </div>
    </div>

    <script src="clock.js"></script>
    <script src="script/prayer.js"></script>
</body>
</html>