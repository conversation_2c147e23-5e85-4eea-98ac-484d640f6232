/**
 * نظام حساب مواقيت الصلاة الدقيق
 * نسخة 1.0
 * تم تطويره خصيصًا لتطبيق مواقيت الصلاة
 */

// كائن حساب مواقيت الصلاة
const PrayerCalculator = {
    // طرق الحساب المختلفة
    methods: {
        MWL: { // رابطة العالم الإسلامي
            name: 'رابطة العالم الإسلامي',
            params: { fajr: 18, isha: 17 }
        },
        ISNA: { // جمعية أمريكا الشمالية الإسلامية
            name: 'جمعية أمريكا الشمالية الإسلامية',
            params: { fajr: 15, isha: 15 }
        },
        Egypt: { // دار الإفتاء المصرية
            name: 'دار الإفتاء المصرية',
            params: { fajr: 19.5, isha: 17.5 }
        },
        Makkah: { // جامعة أم القرى بمكة
            name: 'جامعة أم القرى بمكة',
            params: { fajr: 18.5, isha: '90 min' }
        },
        Karachi: { // جامعة العلوم الإسلامية بكراتشي
            name: 'جامعة العلوم الإسلامية بكراتشي',
            params: { fajr: 18, isha: 18 }
        },
        Tehran: { // معهد الجيوفيزياء بجامعة طهران
            name: 'معهد الجيوفيزياء بجامعة طهران',
            params: { fajr: 17.7, isha: 14, maghrib: 4.5, midnight: 'Jafari' }
        },
        Jafari: { // معهد ليفا بقم (الشيعة)
            name: 'معهد ليفا بقم (الشيعة)',
            params: { fajr: 16, isha: 14, maghrib: 4, midnight: 'Jafari' }
        },
        Jordan: { // الأردن
            name: 'الأردن',
            params: { fajr: 18, isha: 18 }
        }
    },

    // الإعدادات الافتراضية
    settings: {
        method: 'MWL',
        juristic: 'Shafi',
        timeFormat: '12h',
        highLatitudes: 'NightMiddle',
        adjustments: {
            fajr: 0,
            sunrise: 0,
            dhuhr: 0,
            asr: 0,
            maghrib: 0,
            isha: 0
        }
    },

    // تعيين طريقة الحساب
    setMethod: function(method) {
        if (this.methods[method]) {
            this.settings.method = method;
            return true;
        }
        return false;
    },

    // تعيين المذهب الفقهي (للعصر)
    setJuristic: function(juristic) {
        this.settings.juristic = juristic;
        return true;
    },

    // حساب مواقيت الصلاة
    getTimes: function(date, coords, timezone) {
        // التأكد من صحة المدخلات
        if (!date || !coords || !coords.latitude || !coords.longitude) {
            console.error('بيانات غير صحيحة لحساب مواقيت الصلاة');
            return null;
        }

        // تحويل التاريخ إلى كائن Date إذا كان نصًا
        if (typeof date === 'string') {
            date = new Date(date);
        }

        // استخدام التاريخ الحالي إذا لم يتم تحديده
        if (!(date instanceof Date)) {
            date = new Date();
        }

        // الإحداثيات
        const latitude = parseFloat(coords.latitude);
        const longitude = parseFloat(coords.longitude);

        // المنطقة الزمنية
        timezone = timezone || this.getTimeZone(date);

        // الحصول على معلمات طريقة الحساب
        const methodParams = this.methods[this.settings.method].params;

        // حساب زاوية الشمس
        const JDate = this.julian(date.getFullYear(), date.getMonth() + 1, date.getDate()) - longitude / (15 * 24);
        const sunPosition = this.sunPosition(JDate);
        const declination = sunPosition.declination;
        const equation = sunPosition.equation;

        // حساب الأوقات
        const times = {
            fajr: 0,
            sunrise: 0,
            dhuhr: 0,
            asr: 0,
            maghrib: 0,
            isha: 0
        };

        // حساب وقت الظهر
        times.dhuhr = 12 + timezone - longitude / 15 - equation;

        // حساب وقت العصر
        const asrFactor = this.settings.juristic === 'Hanafi' ? 2 : 1;
        const asrAngle = this.asrAngle(declination, latitude, asrFactor);
        times.asr = this.sunAngleToTime(asrAngle, declination, latitude);

        // حساب وقت الفجر
        const fajrAngle = methodParams.fajr;
        times.fajr = this.sunAngleToTime(fajrAngle, declination, latitude, true);

        // حساب وقت الشروق
        times.sunrise = this.sunAngleToTime(0.833, declination, latitude, true);

        // حساب وقت المغرب
        times.maghrib = this.sunAngleToTime(0.833, declination, latitude);

        // حساب وقت العشاء
        if (methodParams.isha && typeof methodParams.isha === 'number') {
            times.isha = this.sunAngleToTime(methodParams.isha, declination, latitude);
        } else {
            times.isha = times.maghrib + 1.5; // 90 دقيقة بعد المغرب
        }

        // تطبيق التعديلات
        for (let i in times) {
            times[i] += this.settings.adjustments[i] / 60;
        }

        // تنسيق الأوقات
        const formattedTimes = {};
        for (let i in times) {
            formattedTimes[i] = this.formatTime(times[i]);
        }

        return formattedTimes;
    },

    // تحويل زاوية الشمس إلى وقت
    sunAngleToTime: function(angle, declination, latitude, isRising = false) {
        let term1 = Math.sin(this.degToRad(angle)) -
                   Math.sin(this.degToRad(declination)) *
                   Math.sin(this.degToRad(latitude));

        let term2 = Math.cos(this.degToRad(declination)) *
                   Math.cos(this.degToRad(latitude));

        let cosine = term1 / term2;

        if (cosine > 1) cosine = 1;
        else if (cosine < -1) cosine = -1;

        let angle2 = this.radToDeg(Math.acos(cosine));
        let time = 12 - angle2 / 15;

        if (!isRising) time = 12 + angle2 / 15;

        return time;
    },

    // حساب زاوية العصر
    asrAngle: function(declination, latitude, factor) {
        const term1 = Math.tan(this.degToRad(Math.abs(latitude - declination)));
        return this.radToDeg(Math.atan(1 / (factor + term1)));
    },

    // حساب موقع الشمس
    sunPosition: function(jd) {
        const D = jd - 2451545.0;
        const g = this.fixAngle(357.529 + 0.98560028 * D);
        const q = this.fixAngle(280.459 + 0.98564736 * D);
        const L = this.fixAngle(q + 1.915 * Math.sin(this.degToRad(g)) + 0.020 * Math.sin(this.degToRad(2 * g)));

        const e = 23.439 - 0.00000036 * D;
        const RA = this.radToDeg(Math.atan2(Math.cos(this.degToRad(e)) * Math.sin(this.degToRad(L)), Math.cos(this.degToRad(L)))) / 15;
        const equation = q / 15 - this.fixHour(RA);
        const declination = this.radToDeg(Math.asin(Math.sin(this.degToRad(e)) * Math.sin(this.degToRad(L))));

        return { declination: declination, equation: equation };
    },

    // تنسيق الوقت
    formatTime: function(time) {
        if (isNaN(time)) return '--:--';

        time = this.fixHour(time + 0.5 / 60); // تقريب إلى أقرب دقيقة

        let hours = Math.floor(time);
        let minutes = Math.floor((time - hours) * 60);

        // دائماً استخدم نظام 12 ساعة بغض النظر عن الإعدادات
        let suffix = hours >= 12 ? 'م' : 'ص';
        hours = hours % 12;
        if (hours === 0) hours = 12;
        return hours + ':' + (minutes < 10 ? '0' + minutes : minutes) + ' ' + suffix;
    },

    // تصحيح الزاوية
    fixAngle: function(a) {
        a = a - 360 * Math.floor(a / 360);
        a = a < 0 ? a + 360 : a;
        return a;
    },

    // تصحيح الساعة
    fixHour: function(h) {
        h = h - 24 * Math.floor(h / 24);
        h = h < 0 ? h + 24 : h;
        return h;
    },

    // تحويل الدرجات إلى راديان
    degToRad: function(d) {
        return (d * Math.PI) / 180.0;
    },

    // تحويل الراديان إلى درجات
    radToDeg: function(r) {
        return (r * 180.0) / Math.PI;
    },

    // حساب التاريخ الجولياني
    julian: function(year, month, day) {
        if (month <= 2) {
            year -= 1;
            month += 12;
        }
        const A = Math.floor(year / 100);
        const B = 2 - A + Math.floor(A / 4);
        const JD = Math.floor(365.25 * (year + 4716)) + Math.floor(30.6001 * (month + 1)) + day + B - 1524.5;
        return JD;
    },

    // الحصول على المنطقة الزمنية
    getTimeZone: function(date) {
        const jan = new Date(date.getFullYear(), 0, 1);
        const jul = new Date(date.getFullYear(), 6, 1);
        const stdTimezoneOffset = Math.max(jan.getTimezoneOffset(), jul.getTimezoneOffset());

        return -stdTimezoneOffset / 60;
    }
};

// قاعدة بيانات المدن مع إحداثياتها الدقيقة
const CITIES_DATABASE = {
    // الأردن
    'عمان': {
        name: 'عمان',
        country: 'الأردن',
        latitude: 31.9552,
        longitude: 35.945,
        timezone: 3,
        method: 'Jordan'
    },
    'إربد': {
        name: 'إربد',
        country: 'الأردن',
        latitude: 32.5556,
        longitude: 35.85,
        timezone: 3,
        method: 'Jordan'
    },
    'الزرقاء': {
        name: 'الزرقاء',
        country: 'الأردن',
        latitude: 32.0667,
        longitude: 36.1,
        timezone: 3,
        method: 'Jordan'
    },
    'العقبة': {
        name: 'العقبة',
        country: 'الأردن',
        latitude: 29.5267,
        longitude: 35.0078,
        timezone: 3,
        method: 'Jordan'
    },

    // السعودية
    'الرياض': {
        name: 'الرياض',
        country: 'السعودية',
        latitude: 24.6408,
        longitude: 46.7728,
        timezone: 3,
        method: 'Makkah'
    },
    'مكة المكرمة': {
        name: 'مكة المكرمة',
        country: 'السعودية',
        latitude: 21.4225,
        longitude: 39.8262,
        timezone: 3,
        method: 'Makkah'
    },
    'المدينة المنورة': {
        name: 'المدينة المنورة',
        country: 'السعودية',
        latitude: 24.5247,
        longitude: 39.5692,
        timezone: 3,
        method: 'Makkah'
    },
    'جدة': {
        name: 'جدة',
        country: 'السعودية',
        latitude: 21.4858,
        longitude: 39.1925,
        timezone: 3,
        method: 'Makkah'
    },

    // مصر
    'القاهرة': {
        name: 'القاهرة',
        country: 'مصر',
        latitude: 30.0444,
        longitude: 31.2358,
        timezone: 2,
        method: 'Egypt'
    },
    'الإسكندرية': {
        name: 'الإسكندرية',
        country: 'مصر',
        latitude: 31.2001,
        longitude: 29.9187,
        timezone: 2,
        method: 'Egypt'
    }
};

// تصدير الكائنات للاستخدام
window.PrayerCalculator = PrayerCalculator;
window.CITIES_DATABASE = CITIES_DATABASE;
