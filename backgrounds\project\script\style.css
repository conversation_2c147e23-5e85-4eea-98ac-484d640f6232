body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    text-align: center;
}

.digital-clock {
    font-size: 2em;
    margin-top: 20px;
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 20px auto;
    max-width: 600px;
}

.weather-container {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.weather-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 15px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    width: 150px;
}

.weather-display img {
    width: 60px;
    height: 60px;
    margin-bottom: 5px;
}

.weather-display .temperature {
    font-size: 2.2em;
    font-weight: bold;
    color: #2c3e50;
    margin: 5px 0;
}

.weather-display .description {
    font-size: 1.1em;
    color: #34495e;
    text-align: center;
}

.analog-clock {
    position: relative;
    width: 200px;
    height: 200px;
    border: 5px solid black;
    border-radius: 50%;
    margin: 20px auto;
    margin-top: 0;
}

.analog-clock .hour-hand,
.analog-clock .minute-hand,
.analog-clock .second-hand {
    position: absolute;
    width: 50%;
    height: 2px;
    background-color: black;
    top: 50%;
    transform-origin: 100%;
}

.settings-btn {
    position: fixed;
    top: 10px;
    left: 10px;
    background-color: #f0f0f0;
    padding: 10px;
    cursor: pointer;
    border-radius: 5px;
}

.settings-menu {
    position: fixed;
    left: 10px;
    top: 50px;
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

#backgroundSelect {
    margin-top: 10px;
}
