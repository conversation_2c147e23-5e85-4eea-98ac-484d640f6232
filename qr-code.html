<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code - ساعة المسجد</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            padding: 20px;
            direction: rtl;
            text-align: center;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .qr-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px solid #4a3b3b;
        }
        
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: right;
        }
        
        .step {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: right;
        }
        
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            display: inline-block;
            text-decoration: none;
        }
        
        .button:hover {
            background-color: #45a049;
        }
        
        .ip-display {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 18px;
            font-weight: bold;
        }
        
        .warning {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕌 ساعة المسجد - QR Code</h1>
        
        <div class="instructions">
            <h3>📱 كيفية تجربة التطبيق على الهاتف المحمول:</h3>
            
            <div class="step">
                <h4>الخطوة 1: تشغيل الخادم المحلي</h4>
                <p>في مجلد المشروع على اللابتوب، شغل الأمر:</p>
                <div class="ip-display">python -m http.server 8000</div>
                <p>أو:</p>
                <div class="ip-display">npx serve . -p 3000</div>
            </div>
            
            <div class="step">
                <h4>الخطوة 2: معرفة عنوان IP</h4>
                <p>في Command Prompt أو Terminal:</p>
                <div class="ip-display">ipconfig</div>
                <p>ابحث عن عنوان مثل: *************</p>
            </div>
            
            <div class="step">
                <h4>الخطوة 3: إنشاء QR Code</h4>
                <p>أدخل عنوان IP الخاص بك أدناه:</p>
                <input type="text" id="ip-input" placeholder="*************" style="
                    padding: 10px;
                    font-size: 16px;
                    border: 2px solid #4a3b3b;
                    border-radius: 5px;
                    width: 200px;
                    text-align: center;
                ">
                <input type="number" id="port-input" placeholder="8000" value="8000" style="
                    padding: 10px;
                    font-size: 16px;
                    border: 2px solid #4a3b3b;
                    border-radius: 5px;
                    width: 80px;
                    text-align: center;
                    margin-right: 10px;
                ">
                <br><br>
                <button class="button" onclick="generateQR()">إنشاء QR Code</button>
            </div>
            
            <div class="step">
                <h4>الخطوة 4: مسح QR Code</h4>
                <p>1. افتح كاميرا الهاتف أو تطبيق QR Scanner</p>
                <p>2. امسح الكود أدناه</p>
                <p>3. انقر على الرابط الذي يظهر</p>
                <p>4. ستفتح ساعة المسجد في Chrome</p>
                <p>5. ابحث عن "إضافة إلى الشاشة الرئيسية" في القائمة</p>
            </div>
        </div>
        
        <div class="qr-container" id="qr-container">
            <h3>QR Code سيظهر هنا</h3>
            <p>أدخل عنوان IP أعلاه وانقر "إنشاء QR Code"</p>
        </div>
        
        <div class="warning">
            <h4>⚠️ ملاحظات مهمة:</h4>
            <ul style="text-align: right;">
                <li>تأكد أن الهاتف واللابتوب على نفس شبكة WiFi</li>
                <li>تأكد أن الخادم يعمل على اللابتوب</li>
                <li>إذا لم يعمل، جرب تعطيل Firewall مؤقتاً</li>
                <li>استخدم Chrome على الهاتف للحصول على أفضل تجربة PWA</li>
            </ul>
        </div>
        
        <div class="step">
            <h3>🌐 البديل: رفع على الإنترنت</h3>
            <p>للحصول على رابط دائم:</p>
            <a href="https://netlify.com" target="_blank" class="button">Netlify (مجاني)</a>
            <a href="https://vercel.com" target="_blank" class="button">Vercel (مجاني)</a>
            <a href="https://pages.github.com" target="_blank" class="button">GitHub Pages</a>
        </div>
    </div>

    <script>
        function generateQR() {
            const ip = document.getElementById('ip-input').value.trim();
            const port = document.getElementById('port-input').value.trim();
            
            if (!ip) {
                alert('يرجى إدخال عنوان IP');
                return;
            }
            
            const url = `http://${ip}:${port}`;
            const qrContainer = document.getElementById('qr-container');
            
            // استخدام خدمة QR Code مجانية
            const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(url)}`;
            
            qrContainer.innerHTML = `
                <h3>📱 امسح هذا الكود بكاميرا الهاتف</h3>
                <img src="${qrCodeUrl}" alt="QR Code" style="max-width: 300px; border: 2px solid #4a3b3b; border-radius: 10px;">
                <div class="ip-display">${url}</div>
                <p>أو انسخ الرابط أعلاه والصقه في متصفح الهاتف</p>
            `;
        }
        
        // محاولة تخمين IP تلقائياً
        window.addEventListener('load', function() {
            // عرض IP المحلي الشائع
            const commonIPs = ['192.168.1.', '192.168.0.', '10.0.0.'];
            const ipInput = document.getElementById('ip-input');
            
            // يمكن للمستخدم إكمال الرقم الأخير
            ipInput.placeholder = '************* (مثال)';
            
            // نصيحة
            setTimeout(() => {
                alert('💡 نصيحة: لمعرفة عنوان IP الخاص بك، افتح Command Prompt واكتب: ipconfig');
            }, 2000);
        });
    </script>
</body>
</html>
