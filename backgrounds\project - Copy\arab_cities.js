// قائمة المدن العربية
const arabCities = {
    'المملكة الأردنية الهاشمية': {
        'عمان': { timezone: 'Asia/Amman', lat: 31.9454, lng: 35.9284 },
        'إربد': { timezone: 'Asia/Amman', lat: 32.5556, lng: 35.8500 },
        'الزرقاء': { timezone: 'Asia/Amman', lat: 32.0728, lng: 36.0929 },
        'المفرق': { timezone: 'Asia/Amman', lat: 32.3429, lng: 36.2079 },
        'السلط': { timezone: 'Asia/Amman', lat: 32.0392, lng: 35.7272 },
        'مادبا': { timezone: 'Asia/Amman', lat: 31.7160, lng: 35.7938 },
        'الكرك': { timezone: 'Asia/Amman', lat: 31.1853, lng: 35.7047 },
        'الطفيلة': { timezone: 'Asia/Amman', lat: 30.8375, lng: 35.6044 },
        'معان': { timezone: 'Asia/Amman', lat: 30.1927, lng: 35.7331 },
        'العقبة': { timezone: 'Asia/Amman', lat: 29.5267, lng: 35.0078 }
    },
    'المملكة العربية السعودية': {
        'مكة المكرمة': { timezone: 'Asia/Riyadh', lat: 21.4225, lng: 39.8262 },
        'المدينة المنورة': { timezone: 'Asia/Riyadh', lat: 24.4672, lng: 39.6111 },
        'الرياض': { timezone: 'Asia/Riyadh', lat: 24.7136, lng: 46.6753 },
        'جدة': { timezone: 'Asia/Riyadh', lat: 21.5433, lng: 39.1728 },
        'الدمام': { timezone: 'Asia/Riyadh', lat: 26.4207, lng: 50.0888 }
    },
    'جمهورية مصر العربية': {
        'القاهرة': { timezone: 'Africa/Cairo', lat: 30.0444, lng: 31.2357 },
        'الإسكندرية': { timezone: 'Africa/Cairo', lat: 31.2001, lng: 29.9187 },
        'أسوان': { timezone: 'Africa/Cairo', lat: 24.0889, lng: 32.8998 },
        'الأقصر': { timezone: 'Africa/Cairo', lat: 25.6872, lng: 32.6396 }
    },
    'الإمارات العربية المتحدة': {
        'دبي': { timezone: 'Asia/Dubai', lat: 25.2048, lng: 55.2708 },
        'أبو ظبي': { timezone: 'Asia/Dubai', lat: 24.4539, lng: 54.3773 },
        'الشارقة': { timezone: 'Asia/Dubai', lat: 25.3463, lng: 55.4209 }
    }
};

// دالة إضافة المدن للقائمة المنسدلة
function populateCitySelect() {
    const citySelect = document.getElementById('city-select');
    if (!citySelect) return;

    // مسح القائمة الحالية
    citySelect.innerHTML = '';

    // إضافة المدن مع تنظيمها في مجموعات
    Object.entries(arabCities).forEach(([country, cities]) => {
        const optgroup = document.createElement('optgroup');
        optgroup.label = country;

        Object.entries(cities).forEach(([cityName, cityData]) => {
            const option = document.createElement('option');
            option.value = cityData.timezone;
            option.dataset.lat = cityData.lat;
            option.dataset.lng = cityData.lng;
            option.textContent = cityName;
            optgroup.appendChild(option);
        });

        citySelect.appendChild(optgroup);
    });
}

// دالة الحصول على إحداثيات المدينة
function getCityCoordinates(cityName) {
    for (const country of Object.values(arabCities)) {
        if (country[cityName]) {
            return {
                lat: country[cityName].lat,
                lng: country[cityName].lng
            };
        }
    }
    // إرجاع إحداثيات عمان كقيمة افتراضية
    return { lat: 31.9454, lng: 35.9284 };
}

// دالة تحديث مواقيت الصلاة للمدينة المحددة
async function updateCityPrayerTimes(cityName) {
    const coords = getCityCoordinates(cityName);
    try {
        const date = new Date();
        const url = `https://api.aladhan.com/v1/calendar?latitude=${coords.lat}&longitude=${coords.lng}&method=4&month=${date.getMonth() + 1}&year=${date.getFullYear()}`;
        
        const response = await fetch(url);
        const data = await response.json();
        
        if (data.data && data.data[date.getDate() - 1]) {
            const timings = data.data[date.getDate() - 1].timings;
            window.prayerTimes = window.prayerTimes || {};
            window.prayerTimes[cityName] = {
                fajr: timings.Fajr,
                sunrise: timings.Sunrise,
                dhuhr: timings.Dhuhr,
                asr: timings.Asr,
                maghrib: timings.Maghrib,
                isha: timings.Isha
            };
            window.currentPrayerTimes = window.prayerTimes[cityName];
            updatePrayerTimesDisplay();
        }
    } catch (error) {
        console.error('خطأ في جلب مواقيت الصلاة للمدينة:', cityName);
        // استخدام المواقيت المحلية كاحتياطي
        useFallbackPrayerTimes(cityName);
    }
}

// إضافة مستمعي الأحداث
document.addEventListener('DOMContentLoaded', () => {
    populateCitySelect();
    
    // مستمع تغيير المدينة
    document.getElementById('city-select')?.addEventListener('change', (e) => {
        const selectedOption = e.target.selectedOptions[0];
        const cityName = selectedOption.textContent;
        updateCityPrayerTimes(cityName);
    });
});

// تصدير الدوال للاستخدام في الملفات الأخرى
// إزالة export لأننا لا نستخدم وحدات ES6
// export {
//     arabCities,
//     populateCitySelect,
//     getCityCoordinates,
//     updateCityPrayerTimes
// };
