let basePrayerTimes = {
    fajr: '05:00', // الفجر
    sunrise: '06:30', // الشروق
    dhuhr: '12:00', // الظهر
    asr: '15:30', // العصر
    maghrib: '18:00', // المغرب
    isha: '19:30' // العشاء
};

function adjustPrayerTimes(timezoneOffset) {
    const adjustedTimes = {};
    
    for (const prayer in basePrayerTimes) {
        const [hours, minutes] = basePrayerTimes[prayer].split(':');
        const prayerTime = new Date();
        prayerTime.setHours(parseInt(hours));
        prayerTime.setMinutes(parseInt(minutes));
        
        // تعديل الوقت حسب المنطقة الزمنية
        const timezoneDiff = parseInt(timezoneOffset) - 3; // 3 هو توقيت مكة المكرمة
        prayerTime.setHours(prayerTime.getHours() + timezoneDiff);
        
        // تنسيق الوقت
        const formattedHours = String(prayerTime.getHours()).padStart(2, '0');
        const formattedMinutes = String(prayerTime.getMinutes()).padStart(2, '0');
        adjustedTimes[prayer] = `${formattedHours}:${formattedMinutes}`;
    }
    
    return adjustedTimes;
}

function displayPrayerTimes(is24Hour) {
    console.log('Displaying prayer times:', basePrayerTimes);
    
    const timezone = document.getElementById('timezone').value;
    const adjustedPrayerTimes = adjustPrayerTimes(timezone);
    console.log('Adjusted prayer times:', adjustedPrayerTimes);

    const formatTime = (time24) => {
        const [hours, minutes] = time24.split(':');
        let hours12 = parseInt(hours);
        let period = "";
        if (!is24Hour) {
            period = hours12 >= 12 ? "PM" : "AM";
            hours12 = hours12 % 12 || 12;
        }
        const formattedHours = String(hours12).padStart(2, '0');
        return is24Hour ? `${hours}:${minutes}` : `${formattedHours}:${minutes} ${period}`;
    };

    // تحديث أوقات الصلوات في المستطيل الأفقي
    const updateTimeElement = (id, time) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = formatTime(time);
            console.log(`Updated ${id} to ${formatTime(time)}`);
        } else {
            console.error(`Element with id ${id} not found`);
        }
    };

    updateTimeElement('fajr-time', adjustedPrayerTimes.fajr);
    updateTimeElement('sunrise-time', adjustedPrayerTimes.sunrise);
    updateTimeElement('dhuhr-time', adjustedPrayerTimes.dhuhr);
    updateTimeElement('asr-time', adjustedPrayerTimes.asr);
    updateTimeElement('maghrib-time', adjustedPrayerTimes.maghrib);
    updateTimeElement('isha-time', adjustedPrayerTimes.isha);
}

let iqamaTimeout;

// إضافة متغيرات للتحكم في مدة الإقامة والتعتيم
let iqamaDuration = 10; // مدة الإقامة بالدقائق
let dimmingDuration = 5; // مدة التعتيم بالدقائق
let isDimmed = false;

function updateIqamaCountdown() {
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentSecond = now.getSeconds();
    
    const timezone = document.getElementById('timezone').value;
    const adjustedPrayerTimes = adjustPrayerTimes(timezone);
    
    // تحويل مواقيت الصلوات إلى دقائق
    const prayerMinutes = {};
    for (const prayer in adjustedPrayerTimes) {
        const [hours, minutes] = adjustedPrayerTimes[prayer].split(':');
        prayerMinutes[prayer] = parseInt(hours) * 60 + parseInt(minutes);
    }
    
    // حساب الوقت الحالي بالدقائق
    const currentMinutes = currentHour * 60 + currentMinute;
    
    // تحديد أقرب صلاة
    let nextPrayer = null;
    let nextPrayerTime = null;
    let minDiff = Infinity;
    
    for (const prayer in prayerMinutes) {
        let diff = prayerMinutes[prayer] - currentMinutes;
        if (diff < 0) {
            diff += 24 * 60; // إضافة 24 ساعة إذا كانت الصلاة في اليوم التالي
        }
        if (diff < minDiff) {
            minDiff = diff;
            nextPrayer = prayer;
            nextPrayerTime = prayerMinutes[prayer];
        }
    }
    
    if (nextPrayer) {
        // حساب الوقت المتبقي حتى الصلاة
        const hoursLeft = Math.floor(minDiff / 60);
        const minutesLeft = minDiff % 60;
        const secondsLeft = 60 - currentSecond;
        
        // تحديث العد التنازلي
        const countdownElement = document.querySelector('.countdown-time');
        countdownElement.textContent = `${String(hoursLeft).padStart(2, '0')}:${String(minutesLeft).padStart(2, '0')}:${String(secondsLeft).padStart(2, '0')}`;
        
        // تحديث اسم الصلاة
        const prayerNames = {
            fajr: 'الفجر',
            dhuhr: 'الظهر',
            asr: 'العصر',
            maghrib: 'المغرب',
            isha: 'العشاء'
        };
        
        const prayerNameElement = document.querySelector('.countdown-label');
        prayerNameElement.textContent = `متبقي حتى صلاة ${prayerNames[nextPrayer]}`;
        prayerNameElement.style.display = 'block';
        
        // تشغيل الأذان عند دخول وقت الصلاة
        if (minDiff === 0 && currentSecond === 0) {
            playAdhan(nextPrayer);
            showCountdownInCenter();
        }
        
        // تشغيل الإقامة بعد مدة الإقامة من الأذان
        if (minDiff === -iqamaDuration && currentSecond === 0) {
            playIqama();
            startDimming();
        }
    }
}

function showCountdownInCenter() {
    const countdownContainer = document.querySelector('.countdown-container');
    countdownContainer.style.position = 'fixed';
    countdownContainer.style.top = '50%';
    countdownContainer.style.left = '50%';
    countdownContainer.style.transform = 'translate(-50%, -50%)';
    countdownContainer.style.zIndex = '1000';
    countdownContainer.style.fontSize = '3em';
    countdownContainer.style.color = '#fff';
    countdownContainer.style.textAlign = 'center';
    
    // إضافة التعتيم للصفحة
    const dimmer = document.createElement('div');
    dimmer.id = 'countdown-dimmer';
    dimmer.style.position = 'fixed';
    dimmer.style.top = '0';
    dimmer.style.left = '0';
    dimmer.style.width = '100%';
    dimmer.style.height = '100%';
    dimmer.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
    dimmer.style.zIndex = '999';
    document.body.appendChild(dimmer);
}

function playAdhan(prayerName) {
    if (document.getElementById('adhanSound').checked) {
        const audio = new Audio();
        if (prayerName === 'fajr') {
            audio.src = 'audio/fajr-adhan.mp3';
        } else {
            audio.src = 'audio/adhan.mp3';
        }
        audio.play();
    }
}

function playIqama() {
    if (document.getElementById('adhanSound').checked) {
        const audio = new Audio('audio/iqama.mp3');
        audio.play();
    }
}

function startDimming() {
    if (!isDimmed) {
        isDimmed = true;
        
        // إزالة عنصر العد التنازلي من المنتصف
        const countdownContainer = document.querySelector('.countdown-container');
        countdownContainer.style.position = '';
        countdownContainer.style.top = '';
        countdownContainer.style.left = '';
        countdownContainer.style.transform = '';
        countdownContainer.style.zIndex = '';
        countdownContainer.style.fontSize = '';
        countdownContainer.style.color = '';
        
        // إزالة تعتيم العد التنازلي
        const countdownDimmer = document.getElementById('countdown-dimmer');
        if (countdownDimmer) {
            document.body.removeChild(countdownDimmer);
        }
        
        // إضافة التعتيم الكامل للصفحة
        const dimmer = document.createElement('div');
        dimmer.id = 'dimmer';
        dimmer.style.position = 'fixed';
        dimmer.style.top = '0';
        dimmer.style.left = '0';
        dimmer.style.width = '100%';
        dimmer.style.height = '100%';
        dimmer.style.backgroundColor = 'rgba(0, 0, 0, 1)';
        dimmer.style.zIndex = '999';
        document.body.appendChild(dimmer);
        
        // إزالة التعتيم بعد مدة التعتيم
        setTimeout(() => {
            isDimmed = false;
            document.body.removeChild(dimmer);
        }, dimmingDuration * 60 * 1000);
    }
}

function initPrayer() {
    const timeFormatToggle = document.getElementById('time-format-toggle');
    const is24Hour = timeFormatToggle.checked;
    displayPrayerTimes(is24Hour);

    timeFormatToggle.addEventListener('change', () => {
        const is24Hour = timeFormatToggle.checked;
        displayPrayerTimes(is24Hour);
    });

    setInterval(updateIqamaCountdown, 1000);

    const timezoneSelect = document.getElementById('timezone-select');
    timezoneSelect.addEventListener('change', () => {
        const is24Hour = document.getElementById('time-format-toggle').checked;
        displayPrayerTimes(is24Hour);
    });
}

document.addEventListener('DOMContentLoaded', initPrayer);

// تحميل المواقيت المحفوظة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const savedTimes = localStorage.getItem('customPrayerTimes');
    if (savedTimes) {
        try {
            const prayerTimes = JSON.parse(savedTimes);
            console.log('Loaded saved times:', prayerTimes);
            
            // تحديث حقول الإدخال
            if (document.getElementById('fajrTime')) {
                document.getElementById('fajrTime').value = prayerTimes.fajr;
                document.getElementById('dhuhrTime').value = prayerTimes.dhuhr;
                document.getElementById('asrTime').value = prayerTimes.asr;
                document.getElementById('maghribTime').value = prayerTimes.maghrib;
                document.getElementById('ishaTime').value = prayerTimes.isha;
            }
            
            // تحديث المتغير basePrayerTimes
            basePrayerTimes = {
                ...basePrayerTimes,
                ...prayerTimes
            };
            
            console.log('Updated basePrayerTimes:', basePrayerTimes);
            
            // تحديث العرض
            const timeFormat = document.getElementById('timeFormat').value === '24';
            displayPrayerTimes(timeFormat);
        } catch (error) {
            console.error('Error loading saved times:', error);
        }
    }
});

// حفظ مواقيت الصلوات
document.getElementById('savePrayerTimes').addEventListener('click', function() {
    try {
        const prayerTimes = {
            fajr: document.getElementById('fajrTime').value,
            dhuhr: document.getElementById('dhuhrTime').value,
            asr: document.getElementById('asrTime').value,
            maghrib: document.getElementById('maghribTime').value,
            isha: document.getElementById('ishaTime').value
        };
        
        console.log('Saving times:', prayerTimes);
        
        // التحقق من صحة المواقيت
        for (const time in prayerTimes) {
            if (!prayerTimes[time] || !prayerTimes[time].match(/^([0-1][0-9]|2[0-3]):[0-5][0-9]$/)) {
                alert('الرجاء إدخال مواقيت صحيحة بتنسيق 24 ساعة (مثال: 18:00)');
                return;
            }
        }
        
        // تحديث المتغير basePrayerTimes
        basePrayerTimes = {
            ...basePrayerTimes,
            ...prayerTimes
        };
        
        console.log('Updated basePrayerTimes:', basePrayerTimes);
        
        // حفظ المواقيت في localStorage
        localStorage.setItem('customPrayerTimes', JSON.stringify(prayerTimes));
        
        // تحديث عرض المواقيت مباشرة
        const timeFormat = document.getElementById('timeFormat').value === '24';
        displayPrayerTimes(timeFormat);
        
        // إظهار رسالة نجاح
        alert('تم حفظ مواقيت الصلوات بنجاح');
    } catch (error) {
        console.error('Error saving prayer times:', error);
        alert('حدث خطأ أثناء حفظ المواقيت');
    }
});

// تحديث مواقيت الصلوات عند تغيير المنطقة الزمنية
document.getElementById('timezone').addEventListener('change', function() {
    const timeFormat = document.getElementById('timeFormat').value === '24';
    displayPrayerTimes(timeFormat);
});

// تحديث مواقيت الصلوات عند تغيير تنسيق الوقت
document.getElementById('timeFormat').addEventListener('change', function() {
    const timeFormat = this.value === '24';
    displayPrayerTimes(timeFormat);
});

// إضافة عناصر التحكم في مدة الإقامة والتعتيم
document.addEventListener('DOMContentLoaded', function() {
    const settingsContainer = document.querySelector('.settings-container');
    
    // إضافة عناصر التحكم في مدة الإقامة
    const iqamaDurationDiv = document.createElement('div');
    iqamaDurationDiv.className = 'setting-item';
    iqamaDurationDiv.innerHTML = `
        <label for="iqamaDuration">مدة الإقامة (دقائق):</label>
        <input type="number" id="iqamaDuration" min="1" max="30" value="${iqamaDuration}">
    `;
    settingsContainer.appendChild(iqamaDurationDiv);
    
    // إضافة عناصر التحكم في مدة التعتيم
    const dimmingDurationDiv = document.createElement('div');
    dimmingDurationDiv.className = 'setting-item';
    dimmingDurationDiv.innerHTML = `
        <label for="dimmingDuration">مدة التعتيم (دقائق):</label>
        <input type="number" id="dimmingDuration" min="1" max="30" value="${dimmingDuration}">
    `;
    settingsContainer.appendChild(dimmingDurationDiv);
    
    // إضافة مستمعي الأحداث لعناصر التحكم
    document.getElementById('iqamaDuration').addEventListener('change', function() {
        iqamaDuration = parseInt(this.value);
        localStorage.setItem('iqamaDuration', iqamaDuration);
    });
    
    document.getElementById('dimmingDuration').addEventListener('change', function() {
        dimmingDuration = parseInt(this.value);
        localStorage.setItem('dimmingDuration', dimmingDuration);
    });
    
    // تحميل القيم المحفوظة
    const savedIqamaDuration = localStorage.getItem('iqamaDuration');
    const savedDimmingDuration = localStorage.getItem('dimmingDuration');
    
    if (savedIqamaDuration) {
        iqamaDuration = parseInt(savedIqamaDuration);
        document.getElementById('iqamaDuration').value = iqamaDuration;
    }
    
    if (savedDimmingDuration) {
        dimmingDuration = parseInt(savedDimmingDuration);
        document.getElementById('dimmingDuration').value = dimmingDuration;
    }
});
