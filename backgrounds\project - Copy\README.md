# تطبيق مواقيت الصلوات

## متطلبات النظام
- Python 3.11 أو أحدث
- اتصال بالإنترنت

## خطوات التثبيت
1. تأكد من تثبيت Python
2. افتح ملف `install_dependencies.bat`
3. انتظر اكتمال تثبيت المكتبات

## تشغيل التطبيق
"C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" "prayer_times.py"

## الميزات
- حساب مواقيت الصلوات لمدن مختلفة
- تحديث تلقائي كل 24 ساعة
- دعم طرق حساب متعددة
- إرسال تنبيهات للصلوات

## مثال الاستخدام
```python
from prayer_times import GlobalPrayerTimesManager

# إنشاء مدير مواقيت الصلوات العالمية
global_manager = GlobalPrayerTimesManager()

# تحديث مواقيت مدينة معينة
global_manager.update_prayer_times('Egypt', 'Cairo')

# جدولة التحديثات التلقائية
global_manager.schedule_updates()
```

## ملاحظات
- يمكنك إضافة دول ومدن جديدة في ملف `countries.json`
- راجع سجل `prayer_times.log` لتتبع التحديثات والأخطاء
