<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار PWA - ساعة المسجد</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            padding: 20px;
            direction: rtl;
            margin: 0;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            display: block;
            width: calc(100% - 20px);
        }
        
        .test-button:hover {
            background-color: #45a049;
        }
        
        .info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .feature-list {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .feature-list ul {
            margin: 0;
            padding-right: 20px;
        }
        
        .feature-list li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار PWA - ساعة المسجد</h1>
        
        <div class="info">
            <h3>هذا الاختبار يتحقق من:</h3>
            <ul>
                <li>دعم PWA في المتصفح</li>
                <li>وجود ملف manifest.json</li>
                <li>تسجيل Service Worker</li>
                <li>إمكانية التثبيت</li>
                <li>عمل التطبيق الأصلي</li>
            </ul>
        </div>
        
        <div id="test-results"></div>
        
        <button class="test-button" onclick="testPWASupport()">
            اختبار دعم PWA
        </button>
        
        <button class="test-button" onclick="testManifest()">
            اختبار ملف Manifest
        </button>
        
        <button class="test-button" onclick="testServiceWorker()">
            اختبار Service Worker
        </button>
        
        <button class="test-button" onclick="testInstallability()">
            اختبار إمكانية التثبيت
        </button>
        
        <button class="test-button" onclick="openMainApp()">
            فتح التطبيق الأصلي
        </button>
        
        <div class="feature-list">
            <h3>الميزات المحفوظة في التطبيق:</h3>
            <ul>
                <li>✅ التصميم الحالي (نفس الألوان والخطوط)</li>
                <li>✅ مدة الإقامة (12 دقيقة أو ما تم ضبطه)</li>
                <li>✅ مدة التعتيم (10 دقائق أو ما تم ضبطه)</li>
                <li>✅ عرض الوقت المتبقي</li>
                <li>✅ تعديل مواقيت الصلاة</li>
                <li>✅ جميع الإعدادات المحفوظة</li>
            </ul>
        </div>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }
        
        function testPWASupport() {
            clearResults();
            addTestResult('بدء اختبار دعم PWA...', 'warning');
            
            // اختبار دعم Service Worker
            if ('serviceWorker' in navigator) {
                addTestResult('✅ Service Worker مدعوم', 'success');
            } else {
                addTestResult('❌ Service Worker غير مدعوم', 'error');
            }
            
            // اختبار دعم Web App Manifest
            if ('onbeforeinstallprompt' in window) {
                addTestResult('✅ تثبيت PWA مدعوم', 'success');
            } else {
                addTestResult('⚠️ تثبيت PWA قد لا يكون مدعوم', 'warning');
            }
            
            // اختبار دعم Notifications
            if ('Notification' in window) {
                addTestResult('✅ الإشعارات مدعومة', 'success');
            } else {
                addTestResult('⚠️ الإشعارات غير مدعومة', 'warning');
            }
            
            // معلومات المتصفح
            addTestResult(`المتصفح: ${navigator.userAgent.split(' ')[0]}`, 'info');
            addTestResult(`النظام: ${navigator.platform}`, 'info');
        }
        
        function testManifest() {
            clearResults();
            addTestResult('بدء اختبار ملف Manifest...', 'warning');
            
            fetch('./manifest.json')
                .then(response => {
                    if (response.ok) {
                        addTestResult('✅ ملف manifest.json موجود', 'success');
                        return response.json();
                    } else {
                        throw new Error('ملف غير موجود');
                    }
                })
                .then(manifest => {
                    addTestResult(`✅ اسم التطبيق: ${manifest.name}`, 'success');
                    addTestResult(`✅ الإصدار: ${manifest.version}`, 'success');
                    addTestResult(`✅ وضع العرض: ${manifest.display}`, 'success');
                    addTestResult(`✅ عدد الأيقونات: ${manifest.icons ? manifest.icons.length : 0}`, 'success');
                })
                .catch(error => {
                    addTestResult(`❌ خطأ في ملف Manifest: ${error.message}`, 'error');
                });
        }
        
        function testServiceWorker() {
            clearResults();
            addTestResult('بدء اختبار Service Worker...', 'warning');
            
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations()
                    .then(registrations => {
                        if (registrations.length > 0) {
                            addTestResult(`✅ Service Worker مسجل: ${registrations.length} تسجيل`, 'success');
                            registrations.forEach((reg, index) => {
                                addTestResult(`📄 التسجيل ${index + 1}: ${reg.scope}`, 'info');
                            });
                        } else {
                            addTestResult('⚠️ لا يوجد Service Worker مسجل', 'warning');
                            addTestResult('سيتم تسجيله عند فتح التطبيق الأصلي', 'info');
                        }
                    })
                    .catch(error => {
                        addTestResult(`❌ خطأ في فحص Service Worker: ${error.message}`, 'error');
                    });
            } else {
                addTestResult('❌ Service Worker غير مدعوم في هذا المتصفح', 'error');
            }
        }
        
        function testInstallability() {
            clearResults();
            addTestResult('بدء اختبار إمكانية التثبيت...', 'warning');
            
            // اختبار إذا كان التطبيق مثبت مسبقاً
            if (window.matchMedia('(display-mode: standalone)').matches) {
                addTestResult('✅ التطبيق مثبت ويعمل كـ PWA', 'success');
            } else {
                addTestResult('📱 التطبيق يعمل في المتصفح', 'info');
            }
            
            // اختبار دعم التثبيت
            if ('onbeforeinstallprompt' in window) {
                addTestResult('✅ يمكن تثبيت التطبيق كـ PWA', 'success');
                addTestResult('ابحث عن أيقونة التثبيت في شريط العنوان', 'info');
            } else {
                addTestResult('⚠️ قد لا يكون التثبيت متاح في هذا المتصفح', 'warning');
            }
            
            // نصائح للتثبيت
            addTestResult('💡 للتثبيت:', 'info');
            addTestResult('- Chrome/Edge: أيقونة التثبيت في شريط العنوان', 'info');
            addTestResult('- Safari: مشاركة > إضافة إلى الشاشة الرئيسية', 'info');
            addTestResult('- Firefox: القائمة > تثبيت', 'info');
        }
        
        function openMainApp() {
            clearResults();
            addTestResult('فتح التطبيق الأصلي...', 'warning');
            
            const appWindow = window.open('./index.html', '_blank');
            
            if (appWindow) {
                addTestResult('✅ تم فتح التطبيق الأصلي', 'success');
                addTestResult('تحقق من الآتي في التطبيق:', 'info');
                addTestResult('1. زر "تثبيت التطبيق" في أسفل اليسار', 'info');
                addTestResult('2. نفس التصميم والألوان', 'info');
                addTestResult('3. جميع الوظائف تعمل بنفس الطريقة', 'info');
                addTestResult('4. الإعدادات محفوظة', 'info');
            } else {
                addTestResult('❌ فشل في فتح التطبيق', 'error');
                addTestResult('تأكد من وجود ملف index.html', 'warning');
            }
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            addTestResult('مرحباً! هذه صفحة اختبار PWA لساعة المسجد', 'success');
            addTestResult('استخدم الأزرار أعلاه لاختبار مختلف جوانب PWA', 'info');
            
            // اختبار سريع للملفات المطلوبة
            setTimeout(() => {
                addTestResult('🔍 فحص سريع للملفات المطلوبة...', 'info');
                
                const requiredFiles = ['./manifest.json', './sw.js', './pwa-simple.js'];
                let allFilesExist = true;
                
                requiredFiles.forEach(file => {
                    fetch(file, { method: 'HEAD' })
                        .then(response => {
                            if (response.ok) {
                                addTestResult(`✅ ${file} موجود`, 'success');
                            } else {
                                addTestResult(`❌ ${file} غير موجود`, 'error');
                                allFilesExist = false;
                            }
                        })
                        .catch(error => {
                            addTestResult(`❌ خطأ في فحص ${file}`, 'error');
                            allFilesExist = false;
                        });
                });
            }, 1000);
        });
    </script>
</body>
</html>
