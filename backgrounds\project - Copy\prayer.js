// تعريف الثوابت في بداية الملف
const LOCATIONS = {
    'الأردن': {
        'عمان': { lat: 31.9539, lng: 35.9106 },
        'الزرقاء': { lat: 32.0625, lng: 36.0931 },
        'إربد': { lat: 32.5556, lng: 35.8500 },
        'العقبة': { lat: 29.5267, lng: 35.0078 },
        'السلط': { lat: 32.0392, lng: 35.7272 },
        'المفرق': { lat: 32.3429, lng: 36.2079 },
        'مادبا': { lat: 31.7160, lng: 35.7938 },
        'جرش': { lat: 32.2808, lng: 35.8997 },
        'عجلون': { lat: 32.3333, lng: 35.7500 },
        'الكرك': { lat: 31.1850, lng: 35.7047 },
        'الطفيلة': { lat: 30.8375, lng: 35.6044 },
        'معان': { lat: 30.1927, lng: 35.7331 }
    },
    'السعودية': {
        'الرياض': { lat: 24.7136, lng: 46.6753 },
        'جدة': { lat: 21.5433, lng: 39.1728 },
        'مكة المكرمة': { lat: 21.4225, lng: 39.8262 },
        'المدينة المنورة': { lat: 24.5247, lng: 39.5692 },
        'الدمام': { lat: 26.4207, lng: 50.0888 },
        'تبوك': { lat: 28.3835, lng: 36.5662 },
        'الطائف': { lat: 21.2667, lng: 40.4167 }
    },
    'مصر': {
        'القاهرة': { lat: 30.0444, lng: 31.2357 },
        'الإسكندرية': { lat: 31.2001, lng: 29.9187 },
        'الجيزة': { lat: 30.0131, lng: 31.2089 },
        'شرم الشيخ': { lat: 27.9158, lng: 34.3300 },
        'الأقصر': { lat: 25.6872, lng: 32.6396 },
        'أسوان': { lat: 24.0889, lng: 32.8998 }
    },
    'الإمارات': {
        'دبي': { lat: 25.2048, lng: 55.2708 },
        'أبو ظبي': { lat: 24.4539, lng: 54.3773 },
        'الشارقة': { lat: 25.3463, lng: 55.4209 },
        'العين': { lat: 24.1302, lng: 55.2808 },
        'رأس الخيمة': { lat: 25.7895, lng: 55.9432 },
        'الفجيرة': { lat: 25.1164, lng: 56.3414 }
    },
    'قطر': {
        'الدوحة': { lat: 25.2854, lng: 51.5310 },
        'الوكرة': { lat: 25.1686, lng: 51.6042 },
        'الخور': { lat: 25.6840, lng: 51.5058 },
        'الريان': { lat: 25.2919, lng: 51.4244 }
    },
    'الكويت': {
        'مدينة الكويت': { lat: 29.3759, lng: 47.9774 },
        'الجهراء': { lat: 29.3375, lng: 47.6581 },
        'حولي': { lat: 29.3375, lng: 48.0203 },
        'الفروانية': { lat: 29.2775, lng: 47.9589 }
    },
    'عُمان': {
        'مسقط': { lat: 23.5880, lng: 58.3829 },
        'صلالة': { lat: 17.0151, lng: 54.0924 },
        'صحار': { lat: 24.3425, lng: 56.7047 },
        'نزوى': { lat: 22.9333, lng: 57.5333 }
    },
    'البحرين': {
        'المنامة': { lat: 26.2285, lng: 50.5860 },
        'المحرق': { lat: 26.2540, lng: 50.6119 },
        'الرفاع': { lat: 26.1174, lng: 50.5558 }
    },
    'لبنان': {
        'بيروت': { lat: 33.8938, lng: 35.5018 },
        'طرابلس': { lat: 34.4334, lng: 35.8389 },
        'صيدا': { lat: 33.5571, lng: 35.3729 }
    },
    'فلسطين': {
        'القدس': { lat: 31.7683, lng: 35.2137 },
        'غزة': { lat: 31.5017, lng: 34.4668 },
        'رام الله': { lat: 31.9018, lng: 35.2044 }
    }
};

const CALCULATION_METHODS = {
    'الأردن': {
        method: 'MWL',
        adjustments: { fajr: 2, sunrise: 0, dhuhr: 3, asr: 0, maghrib: 3, isha: 0 },
        madhab: 'Shafi'
    },
    'السعودية': {
        method: 'Makkah',
        adjustments: { fajr: 0, sunrise: 0, dhuhr: 0, asr: 0, maghrib: 0, isha: 0 },
        madhab: 'Hanbali'
    },
    'مصر': {
        method: 'Egyptian',
        adjustments: { fajr: 0, sunrise: 0, dhuhr: 0, asr: 0, maghrib: 0, isha: 0 },
        madhab: 'Shafi'
    },
    'الإمارات': {
        method: 'Gulf',
        adjustments: { fajr: 0, sunrise: 0, dhuhr: 0, asr: 0, maghrib: 0, isha: 0 },
        madhab: 'Shafi'
    },
    'قطر': {
        method: 'Gulf',
        adjustments: { fajr: 0, sunrise: 0, dhuhr: 0, asr: 0, maghrib: 0, isha: 0 },
        madhab: 'Shafi'
    },
    'الكويت': {
        method: 'Gulf',
        adjustments: { fajr: 0, sunrise: 0, dhuhr: 0, asr: 0, maghrib: 0, isha: 0 },
        madhab: 'Shafi'
    },
    'عُمان': {
        method: 'Gulf',
        adjustments: { fajr: 0, sunrise: 0, dhuhr: 0, asr: 0, maghrib: 0, isha: 0 },
        madhab: 'Shafi'
    },
    'البحرين': {
        method: 'Gulf',
        adjustments: { fajr: 0, sunrise: 0, dhuhr: 0, asr: 0, maghrib: 0, isha: 0 },
        madhab: 'Shafi'
    },
    'لبنان': {
        method: 'MWL',
        adjustments: { fajr: 0, sunrise: 0, dhuhr: 0, asr: 0, maghrib: 0, isha: 0 },
        madhab: 'Shafi'
    },
    'فلسطين': {
        method: 'MWL',
        adjustments: { fajr: 0, sunrise: 0, dhuhr: 0, asr: 0, maghrib: 0, isha: 0 },
        madhab: 'Shafi'
    }
};

const IQAMAH_TIMES = {
    fajr: 30,
    dhuhr: 15,
    asr: 15,
    maghrib: 10,
    isha: 15
};

// المتغيرات العامة
let coordinates;
let params;

// دوال التهيئة
function initializePrayerTimes() {
    coordinates = new Adhan.Coordinates(31.9539, 35.9106); // عمان كموقع افتراضي
    params = new Adhan.CalculationParameters('MWL');
    params.madhab = Adhan.Madhab.Shafi;
    params.adjustments = { fajr: 2, sunrise: 0, dhuhr: 3, asr: 0, maghrib: 3, isha: 0 };
}

// دوال تحديث الواجهة
function updateLocationUI() {
    const countrySelect = document.getElementById('country-select');
    const citySelect = document.getElementById('city-select');
    
    if (!countrySelect || !citySelect) {
        console.error('لم يتم العثور على عناصر القوائم');
        return;
    }

    // معالجة تغيير الدولة
    countrySelect.addEventListener('change', (e) => {
        const selectedCountry = e.target.value;
        if (selectedCountry) {
            updateCityList(selectedCountry);
            const firstCity = Object.keys(LOCATIONS[selectedCountry])[0];
            citySelect.value = firstCity;
            updatePrayerCalculation(selectedCountry, firstCity);
        }
    });

    // معالجة تغيير المدينة
    citySelect.addEventListener('change', (e) => {
        const selectedCountry = countrySelect.value;
        const selectedCity = e.target.value;
        if (selectedCountry && selectedCity) {
            updatePrayerCalculation(selectedCountry, selectedCity);
        }
    });

    // تحديث أولي للمدن
    const defaultCountry = 'الأردن';
    countrySelect.value = defaultCountry;
    updateCityList(defaultCountry);
}

function getPrayerTimes() {
    const date = new Date(); // تحديث التاريخ في كل مرة
    const prayerTimes = new Adhan.PrayerTimes(coordinates, date, params);
    
    // تنسيق الأوقات بالتوقيت العربي
    const formatter = new Intl.DateTimeFormat('ar-JO', {
        hour: 'numeric',
        minute: 'numeric',
        hour12: true
    });

    return {
        fajr: formatter.format(prayerTimes.fajr),
        sunrise: formatter.format(prayerTimes.sunrise),
        dhuhr: formatter.format(prayerTimes.dhuhr),
        asr: formatter.format(prayerTimes.asr),
        maghrib: formatter.format(prayerTimes.maghrib),
        isha: formatter.format(prayerTimes.isha)
    };
}

function getAdhanSound(prayer) {
    switch(prayer) {
        case 'fajr':
            return 'audio/audio_fajr.mp3'; // أذان الفجر
        default:
            return 'audio/audio_dhar.mp3'; // أذان باقي الصلوات
    }
}

// دالة لبدء العد التنازلي للإقامة
function startIqamahCountdown(prayer) {
    const countdownTime = document.querySelector('.countdown-time');
    const nextPrayerText = document.querySelector('.next-prayer-text');
    
    // لا نبدأ العد التنازلي للشروق
    if (prayer === 'sunrise') return;
    
    let remainingMinutes = IQAMAH_TIMES[prayer];
    let remainingSeconds = 0;
    
    // تحديث العرض مباشرة
    countdownTime.textContent = `${String(remainingMinutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
    nextPrayerText.textContent = `الوقت المتبقي للإقامة - ${getPrayerNameInArabic(prayer)}`;
    
    // إيقاف أي عد تنازلي سابق
    if (window.iqamahInterval) {
        clearInterval(window.iqamahInterval);
    }
    
    window.iqamahInterval = setInterval(() => {
        if (remainingSeconds === 0) {
            if (remainingMinutes === 0) {
                clearInterval(window.iqamahInterval);
                // عند انتهاء وقت الإقامة
                countdownTime.textContent = "00:00";
                nextPrayerText.textContent = "حان وقت الإقامة";
                return;
            }
            remainingMinutes--;
            remainingSeconds = 59;
        } else {
            remainingSeconds--;
        }
        
        countdownTime.textContent = `${String(remainingMinutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
    }, 1000);
}

function checkPrayerTime() {
    const times = getPrayerTimes();
    const now = new Date();
    const adhan = document.getElementById('adhan');
    const enableAdhan = document.getElementById('enable-adhan');
    
    if (!window.lastPrayerCheck) {
        window.lastPrayerCheck = {};
    }
    
    // تحويل الأوقات إلى كائنات Date للمقارنة
    const prayerTimesDate = {
        fajr: new Date(now.toDateString() + ' ' + times.fajr),
        sunrise: new Date(now.toDateString() + ' ' + times.sunrise),
        dhuhr: new Date(now.toDateString() + ' ' + times.dhuhr),
        asr: new Date(now.toDateString() + ' ' + times.asr),
        maghrib: new Date(now.toDateString() + ' ' + times.maghrib),
        isha: new Date(now.toDateString() + ' ' + times.isha)
    };
    
    // التحقق من كل وقت صلاة
    for (let prayer in prayerTimesDate) {
        const prayerTime = prayerTimesDate[prayer];
        const currentTimeInMinutes = now.getHours() * 60 + now.getMinutes();
        const prayerTimeInMinutes = prayerTime.getHours() * 60 + prayerTime.getMinutes();
        
        if (currentTimeInMinutes === prayerTimeInMinutes && 
            (!window.lastPrayerCheck[prayer] || 
             window.lastPrayerCheck[prayer] < prayerTimeInMinutes)) {
            
            window.lastPrayerCheck[prayer] = prayerTimeInMinutes;
            
            if (enableAdhan && enableAdhan.checked) {
                adhan.src = getAdhanSound(prayer);
                adhan.currentTime = 0;
                adhan.play()
                    .catch(error => console.error('خطأ في تشغيل الأذان:', error));
            }
            
            startIqamahCountdown(prayer);
            alert(`حان الآن وقت صلاة ${getPrayerNameInArabic(prayer)}`);
        }
    }
}

function getPrayerNameInArabic(prayer) {
    const names = {
        fajr: 'الفجر',
        sunrise: 'الشروق',
        dhuhr: 'الظهر',
        asr: 'العصر',
        maghrib: 'المغرب',
        isha: 'العشاء'
    };
    return names[prayer];
}

// تحديث الوقت الحالي والوقت المتبقي للصلاة القادمة
function updateDisplay() {
    const currentTimeElement = document.getElementById('current-time');
    const nextPrayerElement = document.getElementById('next-prayer');
    const now = new Date();
    const times = getPrayerTimes();
    
    // عرض الوقت الحالي
    currentTimeElement.textContent = `الوقت الحالي: ${now.toLocaleTimeString('ar-SA')}`;
    
    // حساب الصلاة القادمة
    let nextPrayer = null;
    let nextPrayerTime = null;
    
    for (let prayer in times) {
        if (times[prayer] > now) {
            nextPrayer = prayer;
            nextPrayerTime = times[prayer];
            break;
        }
    }
    
    if (nextPrayer) {
        const timeDiff = nextPrayerTime - now;
        const minutes = Math.floor(timeDiff / 1000 / 60);
        nextPrayerElement.textContent = `الصلاة القادمة: ${getPrayerNameInArabic(nextPrayer)} (متبقي ${minutes} دقيقة)`;
    }
}

// تحديث عرض أوقات الصلاة في الواجهة
function updatePrayerTimesDisplay() {
    const times = getPrayerTimes();
    
    // تحديث كل وقت صلاة في الواجهة
    document.getElementById('fajr-time').textContent = times.fajr;
    document.getElementById('sunrise-time').textContent = times.sunrise;
    document.getElementById('dhuhr-time').textContent = times.dhuhr;
    document.getElementById('asr-time').textContent = times.asr;
    document.getElementById('maghrib-time').textContent = times.maghrib;
    document.getElementById('isha-time').textContent = times.isha;
}

// تحديث أوقات الصلاة كل دقيقة
setInterval(updatePrayerTimesDisplay, 60000);

// تحديث فوري عند تحميل الصفحة
updatePrayerTimesDisplay();

// تحديث التوقيت كل 30 ثانية بدلاً من كل ثانية لتحسين الأداء
setInterval(checkPrayerTime, 30000);
setInterval(updateDisplay, 1000);

// دالة تحديث قائمة المدن عند اختيار الدولة
function updateCityList(country) {
    const citySelect = document.getElementById('city-select');
    citySelect.innerHTML = '';

    // إضافة خيار افتراضي للمدن
    const defaultCityOption = document.createElement('option');
    defaultCityOption.value = '';
    defaultCityOption.textContent = 'اختر المدينة';
    citySelect.appendChild(defaultCityOption);

    // إضافة المدن للدولة المختارة
    const cities = Object.keys(LOCATIONS[country]);
    cities.forEach(city => {
        const option = document.createElement('option');
        option.value = city;
        option.textContent = city;
        citySelect.appendChild(option);
    });
}

// دالة تحديث الإحداثيات وطريقة الحساب
function updatePrayerCalculation(country, city) {
    const location = LOCATIONS[country][city];
    const calcMethod = CALCULATION_METHODS[country];
    
    coordinates = new Adhan.Coordinates(location.lat, location.lng);
    params = new Adhan.CalculationParameters(calcMethod.method);
    params.madhab = Adhan.Madhab[calcMethod.madhab];
    params.adjustments = calcMethod.adjustments;
    
    updatePrayerTimesDisplay();
}

// تحديث الساعة التناظرية والرقمية حسب المنطقة الزمنية
function updateClocks(timezone) {
    const now = new Date().toLocaleString('ar-SA', { timeZone: timezone });
    const date = new Date(now);
    
    // تحديث الساعة الرقمية
    document.querySelector('.digital-clock').textContent = 
        date.toLocaleTimeString('ar-SA', { hour12: true });
    
    // تحديث الساعة التناظرية
    const hours = date.getHours() % 12;
    const minutes = date.getMinutes();
    const seconds = date.getSeconds();
    
    const hourHand = document.querySelector('.hour-hand');
    const minuteHand = document.querySelector('.minute-hand');
    const secondHand = document.querySelector('.second-hand');
    
    const hourDeg = (hours * 30) + (minutes * 0.5);
    const minuteDeg = minutes * 6;
    const secondDeg = seconds * 6;
    
    hourHand.style.transform = `rotate(${hourDeg}deg)`;
    minuteHand.style.transform = `rotate(${minuteDeg}deg)`;
    secondHand.style.transform = `rotate(${secondDeg}deg)`;
}

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', () => {
    initializePrayerTimes();
    updateLocationUI();
    updatePrayerCalculation('الأردن', 'عمان');
    
    // تحديث الساعات والأوقات
    setInterval(() => {
        const selectedCountry = document.getElementById('country-select')?.value || 'الأردن';
        const timezone = getTimezone(selectedCountry);
        updateClocks(timezone);
    }, 1000);
    
    setInterval(updatePrayerTimesDisplay, 60000);
    setInterval(checkPrayerTime, 30000);
});

// تحديث المناطق الزمنية
function getTimezone(country) {
    const timezones = {
        'الأردن': 'Asia/Amman',
        'السعودية': 'Asia/Riyadh',
        'مصر': 'Africa/Cairo',
        'الإمارات': 'Asia/Dubai',
        'قطر': 'Asia/Qatar',
        'الكويت': 'Asia/Kuwait',
        'عُمان': 'Asia/Muscat',
        'البحرين': 'Asia/Bahrain',
        'لبنان': 'Asia/Beirut',
        'فلسطين': 'Asia/Jerusalem'
    };
    return timezones[country] || 'Asia/Amman';
} 