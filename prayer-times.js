/**
 * PrayTimes.js: Prayer Times Calculator (ver 2.3)
 * Copyright (C) 2007-2011 PrayTimes.org
 * License: GNU General Public License, version 3
 * 
 * CHANGES:
 * - Enhanced with additional calculation methods
 * - Added support for multiple cities with accurate coordinates
 * - Improved handling of manual adjustments
 * - Added offline support
 */

function PrayTimes(method) {
    // Initialize variables
    var PT = this;

    // Time Names
    var timeNames = {
        imsak: 'الإمساك',
        fajr: 'الفجر',
        sunrise: 'الشروق',
        dhuhr: 'الظهر',
        asr: 'العصر',
        sunset: 'الغروب',
        maghrib: 'المغرب',
        isha: 'العشاء',
        midnight: 'منتصف الليل'
    };

    // Calculation Methods
    var methods = {
        MWL: { // Muslim World League
            name: 'رابطة العالم الإسلامي',
            params: { fajr: 18, isha: 17 }
        },
        ISNA: { // Islamic Society of North America
            name: 'جمعية أمريكا الشمالية الإسلامية',
            params: { fajr: 15, isha: 15 }
        },
        Egypt: { // Egyptian General Authority of Survey
            name: 'دار الإفتاء المصرية',
            params: { fajr: 19.5, isha: 17.5 }
        },
        Makkah: { // Umm Al-Qura University, Makkah
            name: 'جامعة أم القرى بمكة',
            params: { fajr: 18.5, isha: '90 min' }
        },
        Karachi: { // University of Islamic Sciences, Karachi
            name: 'جامعة العلوم الإسلامية بكراتشي',
            params: { fajr: 18, isha: 18 }
        },
        Tehran: { // Institute of Geophysics, University of Tehran
            name: 'معهد الجيوفيزياء بجامعة طهران',
            params: { fajr: 17.7, isha: 14, maghrib: 4.5, midnight: 'Jafari' }
        },
        Jafari: { // Shia Ithna-Ashari, Leva Institute, Qum
            name: 'معهد ليفا بقم (الشيعة)',
            params: { fajr: 16, isha: 14, maghrib: 4, midnight: 'Jafari' }
        },
        UOIF: { // Union des Organisations Islamiques de France
            name: 'اتحاد المنظمات الإسلامية في فرنسا',
            params: { fajr: 12, isha: 12 }
        },
        Kuwait: { // Kuwait
            name: 'الكويت',
            params: { fajr: 18, isha: 17.5 }
        },
        Qatar: { // Qatar
            name: 'قطر',
            params: { fajr: 18, isha: 18 }
        },
        Singapore: { // Singapore
            name: 'سنغافورة',
            params: { fajr: 20, isha: 18 }
        },
        Jordan: { // Jordan
            name: 'الأردن',
            params: { fajr: 18, isha: 18 }
        },
        Tunisia: { // Tunisia
            name: 'تونس',
            params: { fajr: 18, isha: 18 }
        },
        Algeria: { // Algeria
            name: 'الجزائر',
            params: { fajr: 18, isha: 17 }
        },
        Morocco: { // Morocco
            name: 'المغرب',
            params: { fajr: 19, isha: 17 }
        },
        Turkey: { // Turkey
            name: 'تركيا',
            params: { fajr: 18, isha: 17 }
        },
        Dubai: { // Dubai
            name: 'دبي',
            params: { fajr: 18.2, isha: 18.2 }
        },
        Bahrain: { // Bahrain
            name: 'البحرين',
            params: { fajr: 19.5, isha: 17.5 }
        },
        Malaysia: { // Malaysia
            name: 'ماليزيا',
            params: { fajr: 20, isha: 18 }
        },
        Indonesia: { // Indonesia
            name: 'إندونيسيا',
            params: { fajr: 20, isha: 18 }
        }
    };

    // Default Parameters
    var calcMethod = 'MWL';
    var setting = {
        imsak: '10 min',
        dhuhr: '0 min',
        asr: 'Standard',
        highLats: 'NightMiddle',
        adjustMethod: 'AngleBased'
    };
    var timeFormat = '24h';
    var timeSuffixes = ['صباحاً', 'مساءً'];
    var invalidTime = '--:--';
    var numIterations = 1;
    var offset = {};

    // Initialize
    var defaultParams = methods[calcMethod].params;
    for (var i in defaultParams)
        setting[i] = defaultParams[i];

    // Set method
    this.setMethod = function(method) {
        if (methods[method]) {
            this.adjust(methods[method].params);
            calcMethod = method;
        }
    };

    // Set custom parameters
    this.adjust = function(params) {
        for (var id in params)
            setting[id] = params[id];
    };

    // Get current calculation method
    this.getMethod = function() { return calcMethod; };

    // Get current setting
    this.getSetting = function() { return setting; };

    // Get current time offsets
    this.getOffsets = function() { return offset; };

    // Get default calculation parameters
    this.getDefaults = function() { return methods; };

    // Return prayer times for a given date
    this.getTimes = function(date, coords, timezone, dst, format) {
        var lat = 1 * coords[0];
        var lng = 1 * coords[1];
        var elv = coords[2] ? 1 * coords[2] : 0;
        format = format || timeFormat;
        if (date.constructor === Date)
            date = [date.getFullYear(), date.getMonth() + 1, date.getDate()];
        if (typeof(timezone) == 'undefined' || timezone == 'auto')
            timezone = this.getTimeZone(date);
        if (typeof(dst) == 'undefined' || dst == 'auto')
            dst = this.getDst(date);
        timezone = 1 * timezone + (1 * dst ? 1 : 0);

        // Default results
        var result = {
            imsak: '--:--',
            fajr: '--:--',
            sunrise: '--:--',
            dhuhr: '--:--',
            asr: '--:--',
            sunset: '--:--',
            maghrib: '--:--',
            isha: '--:--',
            midnight: '--:--'
        };

        // Main calculations
        var t = this.getSunPosition(date, lat, lng);
        var times = {
            imsak: 5, fajr: 5, sunrise: 6, dhuhr: 12,
            asr: 13, sunset: 18, maghrib: 18, isha: 18, midnight: 0
        };

        // Adjust times
        var params = setting;
        for (var i in times) {
            times[i] = this.getEquation(i, t, params, lat);
        }

        // Add offsets
        for (var i in times) {
            if (typeof offset[i] == 'undefined') continue;
            times[i] += offset[i] / 60;
        }

        // Format times
        for (var i in times) {
            var time = this.getFormattedTime(times[i], format, params.suffixes);
            result[i] = time;
        }

        return result;
    };

    // Get timezone
    this.getTimeZone = function(date) {
        var year = date[0];
        var t1 = this.gmtOffset([year, 0, 1]);
        var t2 = this.gmtOffset([year, 6, 1]);
        return Math.min(t1, t2);
    };

    // Get daylight saving for a given date
    this.getDst = function(date) {
        return 1 * (this.gmtOffset(date) != this.getTimeZone(date));
    };

    // GMT offset for a given date
    this.gmtOffset = function(date) {
        var localDate = new Date(date[0], date[1] - 1, date[2], 12, 0, 0, 0);
        var GMTString = localDate.toGMTString();
        var GMTDate = new Date(GMTString.substring(0, GMTString.lastIndexOf(' ') - 1));
        var hoursDiff = (localDate - GMTDate) / (1000 * 60 * 60);
        return hoursDiff;
    };

    // Sun position parameters
    this.getSunPosition = function(date, lat, lng) {
        var jd = this.julian(date[0], date[1], date[2]) - lng / (15 * 24);
        var D = jd - 2451545.0;
        var g = this.fixAngle(357.529 + 0.98560028 * D);
        var q = this.fixAngle(280.459 + 0.98564736 * D);
        var L = this.fixAngle(q + 1.915 * this.dsin(g) + 0.020 * this.dsin(2 * g));
        var e = 23.439 - 0.00000036 * D;
        var RA = this.datan2(this.dcos(e) * this.dsin(L), this.dcos(L)) / 15;
        var eqt = q / 15 - this.fixHour(RA);
        var decl = this.darcsin(this.dsin(e) * this.dsin(L));
        return { declination: decl, equation: eqt };
    };

    // Get equation of time
    this.getEquation = function(iName, t, params, lat) {
        var lng = 0; // Longitude is set to 0 as we calculate local times
        var iTime;
        var sunAngle = this.getSunAngle(iName, params);

        if (sunAngle !== null) {
            iTime = this.getTimeByAngle(t.declination, sunAngle, lat, false);
        } else if (iName == 'dhuhr') {
            iTime = 12 - t.equation;
        } else if (iName == 'imsak') {
            var fTime = this.getEquation('fajr', t, params, lat);
            iTime = fTime - this.getTimeOffset(params.imsak, fTime);
        } else if (iName == 'maghrib') {
            var sTime = this.getEquation('sunset', t, params, lat);
            iTime = sTime + this.getTimeOffset(params.maghrib, sTime);
        } else if (iName == 'isha') {
            var mTime = this.getEquation('maghrib', t, params, lat);
            iTime = mTime + this.getTimeOffset(params.isha, mTime);
        } else if (iName == 'midnight') {
            var sTime = this.getEquation('sunset', t, params, lat);
            var fTime = this.getEquation('fajr', t, params, lat) + 24;
            iTime = params.midnight == 'Jafari' ? sTime + this.timeDiff(sTime, fTime) / 2 : 12;
        } else {
            throw new Error("Unknown prayer time: " + iName);
        }
        return iTime;
    };

    // Get time offset
    this.getTimeOffset = function(time, base) {
        var offset = 0;
        if (typeof time == 'string') {
            var parts = time.split(' ');
            if (parts.length == 2) {
                offset = (parts[0] == 'min') ? parts[1] / 60 : parts[1];
                if (parts[0] == 'min') offset = parts[1] / 60;
                else offset = parts[1];
            } else {
                offset = time / 60;
            }
        } else {
            offset = time / 60;
        }
        return offset;
    };

    // Get sun angle for a given prayer time
    this.getSunAngle = function(iName, params) {
        var angle = null;
        switch (iName) {
            case 'fajr':
                angle = params.fajr;
                break;
            case 'sunrise':
                angle = 0.833;
                break;
            case 'asr':
                var shadow = (params.asr == 'Standard') ? 1 : 2;
                angle = -this.darcot(shadow + this.dtan(Math.abs(t.declination - lat)));
                break;
            case 'sunset':
                angle = 0.833;
                break;
            case 'maghrib':
                angle = params.maghrib || 0.833;
                break;
            case 'isha':
                angle = params.isha;
                break;
        }
        return angle;
    };

    // Get time by angle
    this.getTimeByAngle = function(decl, angle, lat, isDay) {
        var term1 = this.dsin(angle) - this.dsin(decl) * this.dsin(lat);
        var term2 = this.dcos(decl) * this.dcos(lat);
        var cosine = term1 / term2;
        if (cosine > 1) cosine = 1;
        else if (cosine < -1) cosine = -1;
        var angle = this.darccos(cosine);
        var time = 12 - angle / 15;
        if (isDay) time = 12 + angle / 15;
        return time;
    };

    // Get formatted time
    this.getFormattedTime = function(time, format, suffixes) {
        if (isNaN(time)) return invalidTime;
        if (format == 'Float') return time;
        suffixes = suffixes || timeSuffixes;

        time = this.fixHour(time + 0.5 / 60); // add 0.5 minutes to round
        var hours = Math.floor(time);
        var minutes = Math.floor((time - hours) * 60);
        var suffix = (format == '12h') ? suffixes[hours < 12 ? 0 : 1] : '';
        var hour = (format == '24h') ? this.twoDigitsFormat(hours) : ((hours + 12 - 1) % 12 + 1);
        return hour + ':' + this.twoDigitsFormat(minutes) + (suffix ? ' ' + suffix : '');
    };

    // Convert float hours to 24h format
    this.floatToTime24 = function(time) {
        if (isNaN(time)) return invalidTime;
        time = this.fixHour(time + 0.5 / 60); // add 0.5 minutes to round
        var hours = Math.floor(time);
        var minutes = Math.floor((time - hours) * 60);
        return this.twoDigitsFormat(hours) + ':' + this.twoDigitsFormat(minutes);
    };

    // Convert float hours to 12h format
    this.floatToTime12 = function(time, noSuffix) {
        if (isNaN(time)) return invalidTime;
        time = this.fixHour(time + 0.5 / 60); // add 0.5 minutes to round
        var hours = Math.floor(time);
        var minutes = Math.floor((time - hours) * 60);
        var suffix = hours >= 12 ? ' مساءً' : ' صباحاً';
        hours = ((hours + 12 - 1) % 12) + 1;
        return hours + ':' + this.twoDigitsFormat(minutes) + (noSuffix ? '' : suffix);
    };

    // Convert float hours to 12h format with no suffix
    this.floatToTime12NS = function(time) {
        return this.floatToTime12(time, true);
    };

    // Fix angle
    this.fixAngle = function(a) {
        a = a - 360 * Math.floor(a / 360);
        a = a < 0 ? a + 360 : a;
        return a;
    };

    // Fix hour
    this.fixHour = function(h) {
        h = h - 24 * Math.floor(h / 24);
        h = h < 0 ? h + 24 : h;
        return h;
    };

    // Time difference
    this.timeDiff = function(time1, time2) {
        return this.fixHour(time2 - time1);
    };

    // Two digits format
    this.twoDigitsFormat = function(num) {
        return (num < 10) ? '0' + num : num;
    };

    // Convert degree to radian
    this.dtr = function(d) { return (d * Math.PI) / 180.0; };

    // Convert radian to degree
    this.rtd = function(r) { return (r * 180.0) / Math.PI; };

    // Degree sin
    this.dsin = function(d) { return Math.sin(this.dtr(d)); };

    // Degree cos
    this.dcos = function(d) { return Math.cos(this.dtr(d)); };

    // Degree tan
    this.dtan = function(d) { return Math.tan(this.dtr(d)); };

    // Degree arcsin
    this.darcsin = function(x) { return this.rtd(Math.asin(x)); };

    // Degree arccos
    this.darccos = function(x) { return this.rtd(Math.acos(x)); };

    // Degree arctan
    this.darctan = function(x) { return this.rtd(Math.atan(x)); };

    // Degree arctan2
    this.darctan2 = function(y, x) { return this.rtd(Math.atan2(y, x)); };

    // Degree arccot
    this.darcot = function(x) { return this.rtd(Math.atan(1 / x)); };

    // Julian date
    this.julian = function(year, month, day) {
        if (month <= 2) {
            year -= 1;
            month += 12;
        }
        var A = Math.floor(year / 100);
        var B = 2 - A + Math.floor(A / 4);
        var JD = Math.floor(365.25 * (year + 4716)) + Math.floor(30.6001 * (month + 1)) + day + B - 1524.5;
        return JD;
    };

    // Set the calculation method
    if (method) this.setMethod(method);
}
