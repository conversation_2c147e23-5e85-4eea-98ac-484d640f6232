{"name": "mosque-clock-app", "version": "1.0.0", "description": "تطبيق ساعة المسجد الشامل متعدد المنصات", "main": "index.html", "homepage": "./", "keywords": ["mosque", "prayer-times", "clock", "islamic", "pwa", "android", "desktop", "tv"], "author": {"name": "فريق ساعة المسجد", "email": "<EMAIL>", "url": "https://mosqueclock.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mosque-clock/web-app.git"}, "bugs": {"url": "https://github.com/mosque-clock/web-app/issues"}, "scripts": {"start": "npx serve . -p 3000", "dev": "npx serve . -p 3000 --cors", "build": "echo 'No build step required for static files'", "test": "echo 'Opening test page...' && open test-darkness-timer.html", "serve": "python -m http.server 8000", "android:init": "npx cap add android", "android:sync": "npx cap sync android", "android:run": "npx cap run android", "android:build": "npx cap build android", "android:open": "npx cap open android", "ios:init": "npx cap add ios", "ios:sync": "npx cap sync ios", "ios:run": "npx cap run ios", "ios:build": "npx cap build ios", "ios:open": "npx cap open ios", "electron:init": "npm install electron electron-builder --save-dev", "electron:dev": "electron .", "electron:build": "electron-builder", "electron:dist": "electron-builder --publish=never", "pwa:install": "echo 'PWA can be installed directly from browser'", "deploy": "gh-pages -d .", "lint": "echo 'Linting JavaScript files...'", "format": "echo 'Formatting code...'", "validate": "echo 'Validating HTML and manifest...'", "icons:generate": "echo 'Generate icons for all platforms'", "screenshots": "echo 'Generate screenshots for app stores'"}, "devDependencies": {"@capacitor/cli": "^5.0.0", "electron": "^22.0.0", "electron-builder": "^23.6.0", "gh-pages": "^5.0.0", "serve": "^14.2.0"}, "dependencies": {"@capacitor/android": "^5.0.0", "@capacitor/app": "^5.0.0", "@capacitor/core": "^5.0.0", "@capacitor/device": "^5.0.0", "@capacitor/haptics": "^5.0.0", "@capacitor/ios": "^5.0.0", "@capacitor/keyboard": "^5.0.0", "@capacitor/local-notifications": "^5.0.0", "@capacitor/push-notifications": "^5.0.0", "@capacitor/screen-orientation": "^5.0.0", "@capacitor/splash-screen": "^5.0.0", "@capacitor/status-bar": "^5.0.0", "moment": "^2.29.4", "moment-hijri": "^2.1.2"}, "optionalDependencies": {"electron-is-dev": "^2.0.0", "electron-updater": "^5.3.0"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "pwa": {"name": "ساعة المسجد", "short_name": "ساعة المسجد", "description": "تطبيق شامل لعرض مواقيت الصلاة والساعة للمساجد", "theme_color": "#4a3b3b", "background_color": "#fffbfb", "display": "fullscreen", "orientation": "any", "start_url": "/", "scope": "/"}, "capacitor": {"appId": "com.mosque.clock", "appName": "ساعة المسجد", "webDir": ".", "bundledWebRuntime": false}, "electron": {"main": "electron/main.js", "productName": "ساعة المسجد", "appId": "com.mosque.clock.desktop", "directories": {"output": "dist-electron"}}, "build": {"appId": "com.mosque.clock.desktop", "productName": "ساعة المسجد", "directories": {"output": "dist-electron"}, "files": ["**/*", "!node_modules", "!dist-electron", "!*.md", "!test-*"], "win": {"target": "nsis", "icon": "icons/icon.ico"}, "mac": {"target": "dmg", "icon": "icons/icon.icns"}, "linux": {"target": "AppImage", "icon": "icons/icon.png"}}, "funding": {"type": "github", "url": "https://github.com/sponsors/mosque-clock"}, "config": {"platforms": {"android": {"minSdkVersion": 22, "targetSdkVersion": 33, "compileSdkVersion": 33}, "ios": {"minVersion": "13.0", "targetVersion": "16.0"}, "electron": {"minVersion": "18.0.0"}}, "features": ["prayer-times", "digital-clock", "analog-clock", "countdown-timer", "darkness-mode", "adhan-notifications", "weather-display", "hijri-calendar", "responsive-design", "multi-platform", "offline-capable"]}}