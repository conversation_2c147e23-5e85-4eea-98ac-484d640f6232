import requests
import logging
from datetime import datetime
import pytz

class PrayerTimes:
    def __init__(self, country='Jordan', city='Amman', calculation_method='Egyptian General Authority of Survey'):
        # إحداثيات المدن العربية
        self.city_coordinates = {
            # الأردن
            'Jordan': {
                'Amman': (31.9454, 35.9284),
                'Zarqa': (32.0725, 36.0841),
                'Irbid': (32.5556, 35.8500),
                'Aqaba': (29.5328, 35.0084),
                'Mafraq': (32.3422, 36.2058),
                'Salt': (32.0372, 35.7297),
                'Jerash': (32.2744, 35.8938),
                'Madaba': (31.7167, 35.7917)
            },
            # السعودية
            'Saudi Arabia': {
                'Riyadh': (24.7136, 46.6753),
                'Jeddah': (21.2854, 39.2376),
                'Mecca': (21.3891, 39.8579),
                'Medina': (24.4539, 39.6066),
                'Dammam': (26.4207, 50.1039)
            },
            # مصر
            'Egypt': {
                'Cairo': (30.0444, 31.2357),
                'Alexandria': (31.2001, 29.9187),
                'Giza': (30.0131, 31.2089),
                '<PERSON>bra El Kheima': (30.1280, 31.2436),
                'Port Said': (31.2654, 32.2942)
            },
            # الإمارات
            'United Arab Emirates': {
                'Dubai': (25.2048, 55.2708),
                'Abu Dhabi': (24.4539, 54.3773),
                'Sharjah': (25.3463, 55.4209),
                'Al Ain': (24.1924, 55.764)
            },
            # المغرب
            'Morocco': {
                'Casablanca': (33.5731, -7.5898),
                'Rabat': (34.0209, -6.8241),
                'Fez': (34.0209, -5.0415),
                'Marrakech': (31.6295, -7.9811)
            }
        }

        # المدن الأجنبية
        self.foreign_cities = {
            # الولايات المتحدة
            'United States': {
                'New York': (40.7128, -74.0060),
                'Los Angeles': (34.0522, -118.2437),
                'Chicago': (41.8781, -87.6298),
                'Houston': (29.7604, -95.3698)
            },
            # بريطانيا
            'United Kingdom': {
                'London': (51.5074, -0.1278),
                'Manchester': (53.4808, -2.2426),
                'Birmingham': (52.4862, -1.8904)
            },
            # كندا
            'Canada': {
                'Toronto': (43.6532, -79.3832),
                'Vancouver': (49.2827, -123.1207),
                'Montreal': (45.5017, -73.5673)
            }
        }

        # دمج القواميس
        self.city_coordinates.update(self.foreign_cities)
        
        self.country = country
        self.city = city
        
        # طرق الحساب
        self.calculation_methods = {
            'Muslim World League': 3,
            'Egyptian General Authority of Survey': 5,
            'Umm Al-Qura University, Makkah': 4,
            'University of Islamic Sciences, Karachi': 1,
            'Islamic Society of North America (ISNA)': 2
        }
        self.method = self.calculation_methods.get(calculation_method, 5)

    def get_cities(self, country=None):
        """عرض المدن المتاحة"""
        if country:
            return list(self.city_coordinates.get(country, {}).keys())
        return {country: list(cities.keys()) for country, cities in self.city_coordinates.items()}

    def get_times(self, year=None, month=None, day=None):
        """حساب مواقيت الصلوات"""
        try:
            # تحديد التاريخ
            if year is None:
                now = datetime.now(pytz.timezone('Asia/Amman'))
                year, month, day = now.year, now.month, now.day
            
            # الحصول على الإحداثيات
            latitude, longitude = self.city_coordinates.get(self.country, {}).get(self.city, (31.9454, 35.9284))
            
            # رابط API الأذان
            url = "http://api.aladhan.com/v1/timings"
            params = {
                'latitude': latitude,
                'longitude': longitude,
                'method': self.method,
                'date': f"{day}-{month}-{year}"
            }
            
            # إرسال الطلب
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            # استخراج المواقيت
            if 'data' in data and 'timings' in data['data']:
                timings = data['data']['timings']
                
                # تنسيق المواقيت
                prayer_times = {
                    'Fajr': timings['Fajr'],
                    'Sunrise': timings['Sunrise'],
                    'Dhuhr': timings['Dhuhr'],
                    'Asr': timings['Asr'],
                    'Maghrib': timings['Maghrib'],
                    'Isha': timings['Isha']
                }
                
                # طباعة المواقيت
                print(f"مواقيت الصلوات لـ {self.city}, {self.country}:")
                for prayer, time in prayer_times.items():
                    print(f"{prayer}: {time}")
                
                return prayer_times
            
            return None
        
        except Exception as e:
            logging.error(f"خطأ في حساب مواقيت الصلوات: {e}")
            return None

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء كائن مواقيت الصلوات
    prayer_times = PrayerTimes()
    
    # عرض جميع المدن
    print("المدن المتاحة:")
    cities = prayer_times.get_cities()
    for country, city_list in cities.items():
        print(f"{country}: {', '.join(city_list)}")
    
    # اختبار مواقيت الصلوات لبعض المدن
    test_cities = [
        {'country': 'Jordan', 'city': 'Amman'},
        {'country': 'Saudi Arabia', 'city': 'Mecca'},
        {'country': 'United States', 'city': 'New York'}
    ]
    
    for city_info in test_cities:
        prayer_times = PrayerTimes(**city_info).get_times()
