/**
 * أداة تفعيل/تعطيل النظام المتجاوب
 * تسمح بالتحكم في تشغيل النظام المتجاوب حسب الحاجة
 */

const ResponsiveToggle = {
    isEnabled: false,
    
    // تفعيل النظام المتجاوب
    enable: function() {
        if (typeof PlatformDetector !== 'undefined' && typeof ResponsiveHandler !== 'undefined') {
            console.log('تفعيل النظام المتجاوب...');
            
            PlatformDetector.initialize();
            ResponsiveHandler.initialize();
            
            this.isEnabled = true;
            this.showStatus('تم تفعيل النظام المتجاوب', 'success');
            
            // إضافة زر التعطيل
            this.addToggleButton();
        } else {
            console.error('ملفات النظام المتجاوب غير متوفرة');
            this.showStatus('خطأ: ملفات النظام المتجاوب غير متوفرة', 'error');
        }
    },
    
    // تعطيل النظام المتجاوب
    disable: function() {
        console.log('تعطيل النظام المتجاوب...');
        
        // إزالة الأنماط المتجاوبة
        const responsiveStyle = document.getElementById('responsive-styles');
        if (responsiveStyle) {
            responsiveStyle.remove();
        }
        
        // إزالة فئات النظام المتجاوب
        const body = document.body;
        const responsiveClasses = [
            'tv-layout', 'mobile-layout', 'tablet-layout', 'desktop-layout',
            'portrait-orientation', 'landscape-orientation',
            'pwa-mode', 'touch-device', 'fullscreen-mode', 'tv-remote-navigation'
        ];
        
        responsiveClasses.forEach(className => {
            body.classList.remove(className);
        });
        
        this.isEnabled = false;
        this.showStatus('تم تعطيل النظام المتجاوب', 'warning');
        
        // تحديث زر التبديل
        this.addToggleButton();
    },
    
    // تبديل حالة النظام المتجاوب
    toggle: function() {
        if (this.isEnabled) {
            this.disable();
        } else {
            this.enable();
        }
    },
    
    // إضافة زر التبديل
    addToggleButton: function() {
        // إزالة الزر السابق إن وجد
        const existingButton = document.getElementById('responsive-toggle-btn');
        if (existingButton) {
            existingButton.remove();
        }
        
        const button = document.createElement('button');
        button.id = 'responsive-toggle-btn';
        button.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: ${this.isEnabled ? '#f44336' : '#4CAF50'};
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 10000;
            font-family: Arial, sans-serif;
            direction: rtl;
        `;
        
        button.textContent = this.isEnabled ? 'تعطيل النظام المتجاوب' : 'تفعيل النظام المتجاوب';
        
        button.addEventListener('click', () => {
            this.toggle();
        });
        
        document.body.appendChild(button);
    },
    
    // إظهار رسالة الحالة
    showStatus: function(message, type = 'info') {
        // إزالة الرسالة السابقة
        const existingStatus = document.getElementById('responsive-status');
        if (existingStatus) {
            existingStatus.remove();
        }
        
        const statusDiv = document.createElement('div');
        statusDiv.id = 'responsive-status';
        
        let backgroundColor;
        switch (type) {
            case 'success':
                backgroundColor = '#4CAF50';
                break;
            case 'error':
                backgroundColor = '#f44336';
                break;
            case 'warning':
                backgroundColor = '#ff9800';
                break;
            default:
                backgroundColor = '#2196F3';
        }
        
        statusDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: ${backgroundColor};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 10001;
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: center;
            font-size: 14px;
        `;
        
        statusDiv.textContent = message;
        document.body.appendChild(statusDiv);
        
        // إزالة الرسالة بعد 3 ثوانٍ
        setTimeout(() => {
            if (statusDiv.parentNode) {
                statusDiv.remove();
            }
        }, 3000);
    },
    
    // التحقق من حالة النظام
    getStatus: function() {
        return {
            enabled: this.isEnabled,
            platformDetectorAvailable: typeof PlatformDetector !== 'undefined',
            responsiveHandlerAvailable: typeof ResponsiveHandler !== 'undefined',
            currentLayout: this.isEnabled && typeof ResponsiveHandler !== 'undefined' 
                ? ResponsiveHandler.getCurrentLayout() 
                : null
        };
    },
    
    // تهيئة أداة التبديل
    initialize: function() {
        console.log('تهيئة أداة تبديل النظام المتجاوب...');
        
        // إضافة زر التبديل
        this.addToggleButton();
        
        // إضافة اختصار لوحة المفاتيح (Ctrl+R)
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey && event.key === 'r') {
                event.preventDefault();
                this.toggle();
            }
        });
        
        console.log('تم تهيئة أداة التبديل. اضغط Ctrl+R أو استخدم الزر للتبديل');
    }
};

// تصدير للاستخدام العام
window.ResponsiveToggle = ResponsiveToggle;

// تهيئة تلقائية
document.addEventListener('DOMContentLoaded', function() {
    ResponsiveToggle.initialize();
});

// إضافة دوال سريعة للوحة التحكم
window.enableResponsive = () => ResponsiveToggle.enable();
window.disableResponsive = () => ResponsiveToggle.disable();
window.toggleResponsive = () => ResponsiveToggle.toggle();
window.responsiveStatus = () => ResponsiveToggle.getStatus();
