# test_prayer_times.py
from prayer_times_api import PrayerTimes

def test_prayer_times():
    # إنشاء كائن مواقيت الصلوات
    pt = PrayerTimes()

    # طباعة الدول
    print("الدول المتاحة:")
    countries = list(pt.city_coordinates.keys())
    for country in countries:
        print(f"- {country}")
        
        # طباعة المدن لكل دولة
        cities = list(pt.city_coordinates[country].keys())
        print("  المدن:")
        for city in cities:
            print(f"  - {city}")

if __name__ == "__main__":
    test_prayer_times()