import datetime
import pytz
import logging
import requests
import schedule
import time
from geopy.geocoders import Nominatim
from dotenv import load_dotenv
import os
import json
import threading
from prayer_times_api import PrayerTimes  # استيراد صحيح من prayer_times_api

# تهيئة التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    filename='prayer_times.log'
)

# تحميل المتغيرات البيئية
load_dotenv()

class GlobalPrayerTimesManager:
    def __init__(self, countries_file='countries.json'):
        self.countries = self._load_countries(countries_file)
        self.prayer_times_cache = {}
        self.update_lock = threading.Lock()
        
    def _load_countries(self, countries_file):
        try:
            with open(countries_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logging.error(f"ملف {countries_file} غير موجود")
            return {}
        except json.JSONDecodeError:
            logging.error(f"خطأ في قراءة {countries_file}")
            return {}
    
    def update_prayer_times(self, country, city, date=None):
        try:
            mosque_times = MosquePrayerTimes(country, city)
            times = mosque_times.get_prayer_times(date)
            
            if times:
                key = f"{country}_{city}"
                self.prayer_times_cache[key] = times
                logging.info(f"تم تحديث مواقيت الصلوات لـ {city}, {country}")
                return times
            else:
                logging.warning(f"تعذر تحديث مواقيت الصلوات لـ {city}, {country}")
                return None
        except Exception as e:
            logging.error(f"خطأ في تحديث المواقيت: {e}")
            return None
    
    def get_prayer_times_for_cities(self, cities, date=None):
        """استرداد مواقيت الصلوات لقائمة من المدن"""
        prayer_times = {}
        for country, city in cities:
            # إجبار التحديث في كل مرة
            times = self.update_prayer_times(country, city, date)
            if times:
                key = f"{country}_{city}"
                prayer_times[key] = times
        return prayer_times

    def schedule_updates(self, update_interval_hours=24):
        for country, cities in self.countries.items():
            for city in cities:
                schedule.every(update_interval_hours).hours.do(
                    self.update_prayer_times, country, city
                )
        
        while True:
            schedule.run_pending()
            time.sleep(1)
            
    def get_prayer_times_for_cities_without_update(self, cities):
        prayer_times = {}
        for country, city in cities:
            key = f"{country}_{city}"
            if key in self.prayer_times_cache:
                prayer_times[key] = self.prayer_times_cache[key]
            else:
                times = self.update_prayer_times(country, city)
                if times:
                    prayer_times[key] = times
        return prayer_times

    def schedule_continuous_updates(self, update_interval_hours=24):
        """جدولة تحديثات مستمرة لمواقيت الصلوات"""
        while True:
            try:
                print(f"\n--- بدء التحديث الدوري كل {update_interval_hours} ساعة ---")
                
                # جلب قائمة المدن للتحديث
                cities_to_update = get_comprehensive_city_list()
                
                # تحديث مواقيت الصلوات لكل المدن
                with self.update_lock:
                    current_date = datetime.datetime.now()
                    updated_times = self.get_prayer_times_for_cities(cities_to_update, current_date)
                
                # طباعة المواقيت المحدثة
                for city_key, times in updated_times.items():
                    print(f"\nمواقيت الصلوات المحدثة لـ {city_key} في {current_date.strftime('%Y-%m-%d')}:")
                    for prayer, time_value in times.items():
                        print(f"{prayer}: {time_value}")
                
                # انتظار الفترة المحددة قبل التحديث التالي
                time.sleep(update_interval_hours * 3600)
            
            except Exception as e:
                print(f"خطأ في التحديث الدوري: {e}")
                # انتظار ساعة قبل المحاولة مرة أخرى
                time.sleep(3600)

class MosquePrayerTimes:
    def __init__(self, country, city, calculation_method='Muslim World League'):
        self.country = country
        self.city = city
        self.geolocator = Nominatim(user_agent="mosque_prayer_times_app")
        self.location = self._get_location()
        self.calculation_method = calculation_method
        
    def _get_location(self):
        try:
            location = self.geolocator.geocode(f"{self.city}, {self.country}")
            if not location:
                logging.warning(f"تعذر العثور على الموقع: {self.city}, {self.country}")
                return None
            return location
        except Exception as e:
            logging.error(f"خطأ في تحديد الموقع: {e}")
            return None
    
    def get_prayer_times(self, date=None):
        if not self.location:
            return None
        
        if date is None:
            date = datetime.datetime.now(pytz.UTC)
        
        try:
            pt = PrayerTimes(self.calculation_method)
            times = pt.get_times(
                date.year, 
                date.month, 
                date.day, 
                self.location.latitude, 
                self.location.longitude, 
                self.location.raw.get('address', {}).get('timezone', 'UTC')
            )
            
            return {
                'Fajr': times['fajr'],
                'Sunrise': times['sunrise'],
                'Dhuhr': times['dhuhr'],
                'Asr': times['asr'],
                'Maghrib': times['maghrib'],
                'Isha': times['isha']
            }
        except Exception as e:
            logging.error(f"خطأ في حساب مواقيت الصلوات: {e}")
            return None

def get_comprehensive_city_list():
    """قائمة شاملة بالمدن العربية والأجنبية"""
    return [
        # الدول العربية
        ('Egypt', 'Cairo'),
        ('Egypt', 'Alexandria'),
        ('Egypt', 'Luxor'),
        ('Saudi Arabia', 'Mecca'),
        ('Saudi Arabia', 'Riyadh'),
        ('Saudi Arabia', 'Jeddah'),
        ('United Arab Emirates', 'Dubai'),
        ('United Arab Emirates', 'Abu Dhabi'),
        ('United Arab Emirates', 'Sharjah'),
        
        # الأردن والمدن الداخلية
        ('Jordan', 'Amman'),
        ('Jordan', 'Zarqa'),
        ('Jordan', 'Irbid'),
        ('Jordan', 'Aqaba'),
        ('Jordan', 'Mafraq'),
        ('Jordan', 'Salt'),
        ('Jordan', 'Jerash'),
        ('Jordan', 'Madaba'),
        
        # دول عربية أخرى
        ('Lebanon', 'Beirut'),
        ('Lebanon', 'Tripoli'),
        ('Morocco', 'Casablanca'),
        ('Morocco', 'Rabat'),
        ('Algeria', 'Algiers'),
        ('Tunisia', 'Tunis'),
        ('Iraq', 'Baghdad'),
        ('Syria', 'Damascus'),
        ('Palestine', 'Jerusalem'),
        ('Kuwait', 'Kuwait City'),
        ('Qatar', 'Doha'),
        ('Oman', 'Muscat'),
        ('Bahrain', 'Manama'),
        
        # الدول الأجنبية ذات الأغلبية المسلمة
        ('Turkey', 'Istanbul'),
        ('Turkey', 'Ankara'),
        ('Iran', 'Tehran'),
        ('Iran', 'Mashhad'),
        ('Pakistan', 'Karachi'),
        ('Pakistan', 'Lahore'),
        ('Indonesia', 'Jakarta'),
        ('Indonesia', 'Surabaya'),
        ('Malaysia', 'Kuala Lumpur'),
        
        # دول أخرى
        ('United States', 'New York'),
        ('United States', 'Los Angeles'),
        ('United Kingdom', 'London'),
        ('United Kingdom', 'Manchester'),
        ('France', 'Paris'),
        ('Germany', 'Berlin'),
        ('Canada', 'Toronto')
    ]

def fetch_global_countries():
    """استرداد قائمة الدول والمدن من مصدر موثوق"""
    try:
        api_key = os.getenv('COUNTRIES_API_KEY')
        url = f"https://api.example.com/countries?key={api_key}"
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        logging.error(f"خطأ في جلب بيانات الدول: {e}")
        return {}

def main():
    print("جاري تحميل مواقيت الصلوات...")
    
    # قائمة المدن للاختبار
    test_cities = get_comprehensive_city_list()
    
    # إنشاء مدير مواقيت الصلوات
    global_manager = GlobalPrayerTimesManager()
    
    # جلب مواقيت الصلوات للمدن مع تاريخ محدد للتأكد من التغيير
    import datetime
    current_date = datetime.datetime.now()
    prayer_times = global_manager.get_prayer_times_for_cities(test_cities, current_date)
    
    # طباعة مواقيت الصلوات
    for city_key, times in prayer_times.items():
        print(f"\nمواقيت الصلوات لـ {city_key} في تاريخ {current_date.strftime('%Y-%m-%d')}:")
        for prayer, time_value in times.items():
            print(f"{prayer}: {time_value}")
    
    # بدء التحديث المستمر كل 24 ساعة
    update_thread = threading.Thread(target=global_manager.schedule_continuous_updates, daemon=True)
    update_thread.start()
    
    # الاستمرار في التشغيل
    try:
        while True:
            time.sleep(3600)  # انتظر ساعة واحدة
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج.")

if __name__ == "__main__":
    main()
