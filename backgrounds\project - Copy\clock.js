// المنطقة الزمنية الحالية
let currentTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

// المتغيرات العامة
let currentLocation = { latitude: 31.9539, longitude: 35.9106 }; // عمان كموقع افتراضي
let currentMethod = 'MWL';
let currentMadhab = 'Shafi';

// إضافة طرق الحساب المختلفة
const calculationMethods = {
    'UmmAlQura': "أم القرى",
    'Karachi': "جامعة العلوم الإسلامية، كراتشي",
    'ISNA': "الجمعية الإسلامية لأمريكا الشمالية",
    'MWL': "رابطة العالم الإسلامي",
    'Makkah': "جامعة أم القرى، مكة المكرمة"
};

// إضافة المذاهب
const juristic = {
    "shafi": "الشافعي",
    "hanafi": "الحنفي"
};

// إحداثيات المدن الرئيسية
const cityCoordinates = {
    "الأردن": {
        "عمان": { lat: 31.9539, lng: 35.9106 },
        "الزرقاء": { lat: 32.0725, lng: 36.0841 },
        "إربد": { lat: 32.5556, lng: 35.8500 },
        "العقبة": { lat: 29.5328, lng: 35.0084 }
    },
    "السعودية": {
        "الرياض": { lat: 24.7136, lng: 46.6753 },
        "جدة": { lat: 21.2854, lng: 39.2376 },
        "مكة": { lat: 21.3891, lng: 39.8579 },
        "المدينة": { lat: 24.4539, lng: 39.6066 }
    },
    "مصر": {
        "القاهرة": { lat: 30.0444, lng: 31.2357 },
        "الإسكندرية": { lat: 31.2001, lng: 29.9187 }
    },
    "الإمارات": {
        "دبي": { lat: 25.2048, lng: 55.2708 },
        "أبوظبي": { lat: 24.4539, lng: 54.3773 }
    },
    "قطر": {
        "الدوحة": { lat: 25.2867, lng: 51.5333 }
    },
    "الكويت": {
        "مدينة الكويت": { lat: 29.3759, lng: 47.9774 }
    },
    "عُمان": {
        "مسقط": { lat: 23.5859, lng: 58.4059 }
    },
    "البحرين": {
        "المنامة": { lat: 26.2285, lng: 50.5860 }
    },
    "لبنان": {
        "بيروت": { lat: 33.8938, lng: 35.5018 }
    },
    "فلسطين": {
        "القدس": { lat: 31.7683, lng: 35.2137 }
    },
    "العراق": {
        "بغداد": { lat: 33.3152, lng: 44.3661 }
    },
    "سوريا": {
        "دمشق": { lat: 33.5138, lng: 36.2765 }
    },
    "تركيا": {
        "إسطنبول": { lat: 41.0082, lng: 28.9784 }
    }
};

// إضافة متغيرات للتحكم في التعتيم
let isScreenDimmed = false;
let dimmingDuration = 15; // مدة التعتيم الافتراضية بالدقائق
let dimmingStartTime = null;
let dimmingTimer = null;
let digitalClockTimer = null;

// تحديث مواقيت الصلاة
async function updatePrayerTimes() {
    try {
        const now = new Date();
        const prayerTimes = await calculatePrayerTimesFromCoordinates(
            currentLocation.latitude,
            currentLocation.longitude,
            now,
            currentMethod,
            currentMadhab
        );
        
        if (prayerTimes) {
            // تطبيق التعديلات المحفوظة
            const adjustments = JSON.parse(localStorage.getItem('prayer_time_adjustments')) || {};
            Object.keys(adjustments).forEach(prayer => {
                if (prayerTimes[prayer]) {
                    const [hours, minutes] = prayerTimes[prayer].split(':');
                    const totalMinutes = parseInt(hours) * 60 + parseInt(minutes);
                    const adjustedMinutes = totalMinutes + adjustments[prayer];
                    
                    const newHours = Math.floor(adjustedMinutes / 60);
                    const newMinutes = adjustedMinutes % 60;
                    
                    prayerTimes[prayer] = `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
                }
            });
            
            // تحديث واجهة المستخدم
            document.getElementById('fajr-time').textContent = prayerTimes.Fajr;
            document.getElementById('sunrise-time').textContent = prayerTimes.Sunrise;
            document.getElementById('dhuhr-time').textContent = prayerTimes.Dhuhr;
            document.getElementById('asr-time').textContent = prayerTimes.Asr;
            document.getElementById('maghrib-time').textContent = prayerTimes.Maghrib;
            document.getElementById('isha-time').textContent = prayerTimes.Isha;
            
            // تخزين المواقيت محلياً
            localStorage.setItem('last_prayer_times', JSON.stringify({
                times: prayerTimes,
                timestamp: now.getTime(),
                location: currentLocation,
                method: currentMethod,
                madhab: currentMadhab
            }));
        } else {
            console.warn('لم يتم العثور على مواقيت الصلاة');
            // استخدام المواقيت المحفوظة سابقاً
            const savedTimes = localStorage.getItem('last_prayer_times');
            if (savedTimes) {
                const data = JSON.parse(savedTimes);
                document.getElementById('fajr-time').textContent = data.times.Fajr;
                document.getElementById('sunrise-time').textContent = data.times.Sunrise;
                document.getElementById('dhuhr-time').textContent = data.times.Dhuhr;
                document.getElementById('asr-time').textContent = data.times.Asr;
                document.getElementById('maghrib-time').textContent = data.times.Maghrib;
                document.getElementById('isha-time').textContent = data.times.Isha;
            }
        }
    } catch (error) {
        console.error('خطأ في تحديث مواقيت الصلاة:', error);
        // استخدام المواقيت المحفوظة سابقاً
        const savedTimes = localStorage.getItem('last_prayer_times');
        if (savedTimes) {
            const data = JSON.parse(savedTimes);
            document.getElementById('fajr-time').textContent = data.times.Fajr;
            document.getElementById('sunrise-time').textContent = data.times.Sunrise;
            document.getElementById('dhuhr-time').textContent = data.times.Dhuhr;
            document.getElementById('asr-time').textContent = data.times.Asr;
            document.getElementById('maghrib-time').textContent = data.times.Maghrib;
            document.getElementById('isha-time').textContent = data.times.Isha;
        }
    }
}

// تحديث الموقع
function updateLocation(country, city) {
    if (cityCoordinates[country] && cityCoordinates[country][city]) {
        currentLocation = cityCoordinates[country][city];
        updatePrayerTimes();
    }
}

// تحديث طريقة الحساب
function updateCalculationMethod(method) {
    if (calculationMethods[method]) {
        currentMethod = method;
        updatePrayerTimes();
    }
}

// تحديث المذهب
function updateMadhab(madhab) {
    if (juristic[madhab]) {
        currentMadhab = madhab;
        updatePrayerTimes();
    }
}

// تحديث الساعة
function updateClock() {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    
    document.getElementById('clock').textContent = `${hours}:${minutes}:${seconds}`;
}

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', () => {
    // تحميل المواقيت المحفوظة
    const savedTimes = localStorage.getItem('last_prayer_times');
    if (savedTimes) {
        const data = JSON.parse(savedTimes);
        const now = new Date();
        const lastUpdate = new Date(data.timestamp);
        
        // تحديث المواقيت إذا مرت ساعة أو تغير الموقع/الطريقة
        if (now.getTime() - lastUpdate.getTime() > 3600000 ||
            data.location.latitude !== currentLocation.latitude ||
            data.location.longitude !== currentLocation.longitude ||
            data.method !== currentMethod ||
            data.madhab !== currentMadhab) {
            updatePrayerTimes();
        } else {
            // استخدام المواقيت المحفوظة
            document.getElementById('fajr-time').textContent = data.times.Fajr;
            document.getElementById('sunrise-time').textContent = data.times.Sunrise;
            document.getElementById('dhuhr-time').textContent = data.times.Dhuhr;
            document.getElementById('asr-time').textContent = data.times.Asr;
            document.getElementById('maghrib-time').textContent = data.times.Maghrib;
            document.getElementById('isha-time').textContent = data.times.Isha;
        }
    } else {
        updatePrayerTimes();
    }
    
    // تحديث الساعة كل ثانية
    setInterval(updateClock, 1000);
    
    // تحديث المواقيت كل ساعة
    setInterval(updatePrayerTimes, 3600000);
});

// تحديث النص كل 30 ثانية
setInterval(updateText, 30000);

// التحديث الأولي للنص
document.addEventListener('DOMContentLoaded', () => {
    updateText();
});

document.addEventListener("DOMContentLoaded", () => {
    // استرجاع الإعدادات المحفوظة
    const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    const savedBackground = localStorage.getItem('selectedBackground') || 'images/background1.jpg';
    
    // تطبيق الإعدادات المحفوظة
    const citySelect = document.getElementById("city-select");
    const backgroundSelect = document.getElementById("backgroundSelect");
    
    if (citySelect) {
        citySelect.value = savedCity;
    }
    if (backgroundSelect) {
        backgroundSelect.value = savedBackground;
        document.body.style.backgroundImage = `url('${savedBackground}')`;
    }

    setupSettings();
    setupAnalogClock();
    setupTimezone();
    updateAll();
    
    setInterval(updateAll, 1000);
});

function setupTimezone() {
    const timezoneSelect = document.getElementById("timezone-select");
    timezoneSelect.value = currentTimezone;
    
    timezoneSelect.addEventListener("change", (e) => {
        currentTimezone = e.target.value;
        updateAll();
    });
}

function updateAll() {
    updateAnalogClock();
    updateDigitalClock();
    updateDate();
    const citySelect = document.getElementById("city-select");
    if (citySelect) {
        updatePrayerTimes(citySelect.value);
    } else {
        updatePrayerTimes(); // استخدام القيمة الافتراضية
    }
}

function setupSettings() {
    const settingsBtn = document.querySelector(".settings-btn");
    const settingsMenu = document.querySelector(".settings-menu");

    settingsBtn.addEventListener("click", () => {
        settingsMenu.classList.toggle("active");
    });

    document.addEventListener("click", (e) => {
        if (!settingsMenu.contains(e.target) && !settingsBtn.contains(e.target)) {
            settingsMenu.classList.remove("active");
        }
    });

    const backgroundSelect = document.getElementById("backgroundSelect");
    backgroundSelect.addEventListener("change", (e) => {
        const selectedBackground = e.target.value;
        document.body.style.backgroundImage = `url('${selectedBackground}')`;
        localStorage.setItem('selectedBackground', selectedBackground);
    });

    const citySelect = document.getElementById("city-select");
    if (citySelect) {
        citySelect.addEventListener("change", (e) => {
            const selectedCity = e.target.value;
            localStorage.setItem('selectedCity', selectedCity);
            updatePrayerTimes(selectedCity);
        });
    }

    // إضافة وظائف التحكم في النص
    const increaseTextButton = document.getElementById("increase-text");
    const decreaseTextButton = document.getElementById("decrease-text");
    const textColorSelect = document.getElementById("text-color-select");
    const textOverlay = document.getElementById("text-overlay");
    const timeFormatSelect = document.getElementById("time-format-select");

    increaseTextButton.addEventListener("click", () => {
        const currentSize = parseFloat(getComputedStyle(textOverlay).fontSize);
        const newSize = (currentSize * 1.2) + "px";
        textOverlay.style.fontSize = newSize;
        saveTextSettings(newSize, textOverlay.style.color);
    });

    decreaseTextButton.addEventListener("click", () => {
        const currentSize = parseFloat(getComputedStyle(textOverlay).fontSize);
        const newSize = (currentSize * 0.8) + "px";
        textOverlay.style.fontSize = newSize;
        saveTextSettings(newSize, textOverlay.style.color);
    });

    textColorSelect.addEventListener("change", (e) => {
        textOverlay.style.color = e.target.value;
        saveTextSettings(textOverlay.style.fontSize, e.target.value);
    });

    timeFormatSelect.addEventListener("change", () => {
        updateAll();
    });

    addCalculationOptions();

    // تحميل الإعدادات المحفوظة
    loadTextSettings();
}

function setupAnalogClock() {
    const clock = document.querySelector(".analog-clock");
    
    // إزالة الأرقام القديمة إن وجدت
    const oldNumbers = clock.querySelectorAll('.number');
    oldNumbers.forEach(num => num.remove());

    // إضافة الأرقام بالترتيب الصحيح
    for (let i = 1; i <= 12; i++) {
        const number = document.createElement("div");
        number.className = "number";
        // تعديل الزاوية لتبدأ من الأعلى (12) وتدور في اتجاه عقارب الساعة
        const angle = (i * 30 - 90) * (Math.PI / 180);
        const radius = 65; // نصف قطر دائرة الأرقام
        
        // حساب المواقع مع تصحيح الاتجاه
        const x = radius * Math.cos(angle);
        const y = radius * Math.sin(angle);
        
        number.style.left = `calc(50% + ${x}px)`;
        number.style.top = `calc(50% + ${y}px)`;
        number.textContent = i;
        clock.appendChild(number);
    }
}

// تعديل دالة updateAnalogClock لتطبيق التوقيت الصيفي بشكل صحيح
function updateAnalogClock() {
    const now = new Date();
    const seasonalTime = document.getElementById('seasonal-time').value;
    
    let hours = now.getHours();
    const minutes = now.getMinutes();
    const seconds = now.getSeconds();
    
    // تعديل الساعات حسب التوقيت الصيفي
    if (seasonalTime === 'summer') {
        hours = (hours + 1) % 24;
    }
    
    // التحويل إلى نظام 12 ساعة عن طريق أخذ باقي القسمة بعد 12
    const displayHours = hours % 12;
    
    document.querySelector(".hour-hand").style.transform = 
        `rotate(${(displayHours * 30) + (minutes / 2)}deg)`;
    document.querySelector(".minute-hand").style.transform = 
        `rotate(${minutes * 6}deg)`;
    document.querySelector(".second-hand").style.transform = 
        `rotate(${seconds * 6}deg)`;
}

// تعديل دالة updateDigitalClock لتعمل بنظام 12 و24 ساعة بشكل صحيح
function updateDigitalClock() {
    const now = new Date();
    const timeFormat = document.getElementById('time-format-select').value;
    const seasonalTime = document.getElementById('seasonal-time').value;
    
    let hours = now.getHours();
    // تعديل الساعات حسب التوقيت الصيفي
    if (seasonalTime === 'summer') {
        hours = (hours + 1) % 24;
    }
    
    const minutes = now.getMinutes();
    const seconds = now.getSeconds();
    let timeString = "";
    
    if (timeFormat === '12') {
        // تحويل الساعات إلى نظام 12 ساعة
        const period = hours >= 12 ? 'م' : 'ص';
        const displayHours = hours % 12 || 12;
        timeString = `${String(displayHours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')} ${period}`;
    } else {
        // نظام 24 ساعة بدون تحويل
        timeString = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    }
    
    document.querySelector('.digital-clock').textContent = timeString;
}

// تحديث الساعات كل ثانية
setInterval(() => {
    updateAnalogClock();
    updateDigitalClock();
}, 1000);

function updateDate() {
    const now = new Date();
    
    // تحديث التاريخ الميلادي
    const gregorianDate = new Intl.DateTimeFormat("ar-EG", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
        timeZone: currentTimezone
    }).format(now);
    document.querySelector(".gregorian-date").textContent = `التاريخ الميلادي: ${gregorianDate}`;

    // تحديث التاريخ الهجري
    const hijriDate = new Intl.DateTimeFormat("ar-SA-u-ca-islamic", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
        timeZone: currentTimezone,
        numberingSystem: "arab"
    }).format(now);
    document.querySelector(".hijri-date").textContent = `التاريخ الهجري: ${hijriDate}`;
}

// تحديث التاريخ كل دقيقة
setInterval(updateDate, 60000);

// إضافة التحديث الأولي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    updateDate();
});

// تحديث التاريخ عند تغيير المنطقة الزمنية
const timezoneSelect = document.getElementById('timezone-select');
if (timezoneSelect) {
    timezoneSelect.addEventListener('change', () => {
        currentTimezone = timezoneSelect.value;
        updateDate();
    });
}

// تحديث التاريخ عند منتصف الليل
setInterval(() => {
    const now = new Date();
    if (now.getHours() === 0 && now.getMinutes() === 0) {
        updateDate();
    }
}, 60000);

// مواقيت الصلاة الثابتة
window.prayerTimes = {
    'Asia/Amman': {
        fajr: '05:00',     // الفجر
        sunrise: '06:30',  // الشروق
        dhuhr: '12:00',    // الظهر
        asr: '15:00',      // العصر
        maghrib: '18:00',  // المغرب
        isha: '19:30'      // العشاء
    }
};

// تحديث نظام إدارة مواقيت الصلاة
const prayerManager = {
    // تحديث مواقيت الصلاة
    updateTimes(city = 'Asia/Amman') {
        try {
            // التحقق من وجود المدينة
            if (!city) {
                throw new Error("الرجاء تحديد المدينة");
            }
            
            // محاولة تحديث المواقيت
            const times = FIXED_PRAYER_TIMES[city] || FIXED_PRAYER_TIMES['Asia/Amman'];
            if (!times) {
                throw new Error("فشل في الحصول على مواقيت الصلاة");
            }
            
            // تحديث المواقيت العالمية
            window.prayerTimes = {
                [city]: times
            };

            // تحديث العرض
            const timeFormat = document.getElementById('time-format-select')?.value || '24';
            updatePrayerTimesDisplay(times, timeFormat);
            displayRemainingPrayerTimes();
            
            return times;
        } catch (error) {
            console.error("خطأ في تحديث المواقيت:", error);
            throw error;
        }
    },

    // تحديث الصلاة القادمة
    updateNextPrayer() {
        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes();
        const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
        const times = window.prayerTimes?.[currentCity] || FIXED_PRAYER_TIMES[currentCity];
        
        if (!times) {
            console.warn('مواقيت الصلاة غير متوفرة');
            return;
        }

        const nextPrayer = findNextPrayer(this.convertTimesToMinutes(times), currentTime);
        if (nextPrayer) {
            document.querySelector('.next-prayer-text').textContent = `الصلاة القادمة: ${nextPrayer.arabicName}`;
        }
    },

    // تحويل المواقيت إلى دقائق
    convertTimesToMinutes(times) {
        const result = {};
        for (const [prayer, time] of Object.entries(times)) {
            const [hours, minutes] = time.split(':').map(Number);
            result[prayer] = hours * 60 + minutes;
        }
        return result;
    }
};

// تحديث مواقيت الصلاة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    try {
        const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
        prayerManager.updateTimes(savedCity);
        // تحديث كل دقيقة
        setInterval(() => prayerManager.updateNextPrayer(), 60000);
    } catch (error) {
        console.error('خطأ في تهيئة مواقيت الصلاة:', error);
    }
});

// تحديث دالة updateWeather لتكون أكثر مرونة مع الأخطاء
async function updateWeather() {
    try {
        const response = await fetch('https://api.aladhan.com/v1/timingsByCity?city=Dubai&country=UAE&method=8');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        
        // تحديث العرض
        const weatherDisplay = document.querySelector('.weather-display');
        if (weatherDisplay) {
            const temperatureDiv = weatherDisplay.querySelector('.temperature');
            const descriptionDiv = weatherDisplay.querySelector('.description');
            
            if (data.data?.temperature) {
                temperatureDiv.textContent = `${Math.round(data.data.temperature)}°C`;
            } else {
                temperatureDiv.textContent = '--°C';
            }
            
            descriptionDiv.textContent = 'تم تحديث البيانات';
        }
    } catch (error) {
        console.warn('خطأ في تحديث الطقس:', error);
        // استخدام البيانات المخزنة محلياً
        const cachedWeather = localStorage.getItem('weatherData');
        if (cachedWeather) {
            const weatherData = JSON.parse(cachedWeather);
            updateWeatherDisplay(weatherData);
        }
    }
}

// دالة لجلب مواقيت الصلاة من API عالمي
async function fetchPrayerTimes(city, country) {
    try {
        const response = await fetch(`https://api.aladhan.com/v1/timingsByCity?city=${city}&country=${country}&method=3`);
        if (!response.ok) {
            throw new Error(`فشل في جلب المواقيت: ${response.status}`);
        }
        
        const data = await response.json();
        if (!data.data?.timings) {
            throw new Error("بيانات غير صالحة من API");
        }

        return {
            fajr: data.data.timings.Fajr,
            sunrise: data.data.timings.Sunrise,
            dhuhr: data.data.timings.Dhuhr,
            asr: data.data.timings.Asr,
            maghrib: data.data.timings.Maghrib,
            isha: data.data.timings.Isha
        };
    } catch (error) {
        console.error("خطأ في جلب مواقيت الصلاة:", error);
        return null;
    }
}

// كائن يحتوي على معلومات المدن
const CITIES_INFO = {
    // ألمانيا
    'Europe/Berlin': {
        city: 'Berlin',
        country: 'Germany',
        arabicName: 'برلين'
    },
    'Europe/Munich': {
        city: 'Munich',
        country: 'Germany',
        arabicName: 'ميونخ'
    },
    'Europe/Hamburg': {
        city: 'Hamburg',
        country: 'Germany',
        arabicName: 'هامبورغ'
    },
    'Europe/Frankfurt': {
        city: 'Frankfurt',
        country: 'Germany',
        arabicName: 'فرانكفورت'
    },
    // المدن العربية
    'Asia/Riyadh': {
        city: 'Riyadh',
        country: 'Saudi Arabia',
        arabicName: 'الرياض'
    },
    'Asia/Dubai': {
        city: 'Dubai',
        country: 'UAE',
        arabicName: 'دبي'
    },
    'Asia/Amman': {
        city: 'Amman',
        country: 'Jordan',
        arabicName: 'عمّان'
    },
    // إضافة المزيد من المدن حسب الحاجة
};

// تحديث دالة updatePrayerTimes لتدعم التحديث المستمر
async function updatePrayerTimes(timezone = 'Asia/Amman') {
    try {
        const cityInfo = CITIES_INFO[timezone];
        if (!cityInfo) {
            throw new Error(`معلومات المدينة غير متوفرة: ${timezone}`);
        }

        // جلب مواقيت جديدة من API
        const times = await fetchPrayerTimes(cityInfo.city, cityInfo.country);
        if (!times) {
            throw new Error("فشل في جلب المواقيت من API");
        }

        const timeFormat = document.getElementById('time-format-select')?.value || '24';
        const seasonalTime = document.getElementById('seasonal-time')?.value || 'winter';
        let adjustedTimes = { ...times };

        if (seasonalTime === 'summer') {
            Object.keys(adjustedTimes).forEach(prayer => {
                adjustedTimes[prayer] = addOffsetToTime(times[prayer], 60);
            });
        }

        // تحديث العرض
        updatePrayerTimesDisplay(adjustedTimes, timeFormat);

        // تحديث المواقيت العالمية
        window.prayerTimes = {
            [timezone]: adjustedTimes
        };

        // حفظ في التخزين المحلي
        localStorage.setItem('prayerTimes', JSON.stringify({
            times: window.prayerTimes,
            lastUpdate: Date.now(),
            city: cityInfo.arabicName
        }));

        // تحديث عرض الصلاة القادمة
        displayRemainingPrayerTimes();

        return adjustedTimes;
    } catch (error) {
        console.error('خطأ في تحديث مواقيت الصلاة:', error);
        return fallbackToPrayerTimes(timezone);
    }
}

// دالة احتياطية في حالة فشل API
function fallbackToPrayerTimes(timezone) {
    const cachedTimes = localStorage.getItem('prayerTimes');
    if (cachedTimes) {
        const savedTimes = JSON.parse(cachedTimes);
        if (savedTimes.times[timezone]) {
            console.log('استخدام المواقيت المخزنة محلياً');
            return savedTimes.times[timezone];
        }
    }
    return FIXED_PRAYER_TIMES[timezone] || FIXED_PRAYER_TIMES['Asia/Amman'];
}

// تحديث قائمة المدن في واجهة المستخدم
function updateCityList() {
    const citySelect = document.getElementById('city-select');
    if (!citySelect) return;

    citySelect.innerHTML = '';
    Object.entries(CITIES_INFO).forEach(([timezone, info]) => {
        const option = document.createElement('option');
        option.value = timezone;
        option.textContent = info.arabicName;
        citySelect.appendChild(option);
    });
}

// تعديل setupAutomaticUpdates لتكون أقل تكراراً
function setupAutomaticUpdates() {
    // تحديث المواقيت مرة واحدة في اليوم عند منتصف الليل
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const timeUntilMidnight = tomorrow - now;
    
    setTimeout(async () => {
        const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
        await updatePrayerTimes(currentCity);
        setupAutomaticUpdates();
    }, timeUntilMidnight);
}

// تحديث مستمع تحميل الصفحة
document.addEventListener('DOMContentLoaded', async () => {
    updateCityList();
    const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    await updatePrayerTimes(savedCity);
    setupAutomaticUpdates();
    
    const citySelect = document.getElementById('city-select');
    if (citySelect) {
        citySelect.addEventListener('change', async (e) => {
            const selectedCity = e.target.value;
            localStorage.setItem('selectedCity', selectedCity);
            await updatePrayerTimes(selectedCity);
        });
    }
});

// تحديث دالة updatePrayerTimesDisplay لتدعم التوقيت الصيفي/الشتوي
function updatePrayerTimesDisplay(times, timeFormat) {
    const seasonalTime = document.getElementById('seasonal-time')?.value || 'winter';
    
    const formatTime = (time) => {
        if (!time) return '';
        
        // تطبيق التوقيت الصيفي إذا كان مفعلاً
        let [hours, minutes] = time.split(':').map(Number);
        if (seasonalTime === 'summer') {
            hours = (hours + 1) % 24;
        }

        if (timeFormat === '12') {
            const period = hours >= 12 ? 'م' : 'ص';
            hours = hours % 12 || 12;
            return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')} ${period}`;
        }
        return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
    };

    // تحديث عرض المواقيت
    document.getElementById('fajr-time').textContent = formatTime(times.fajr);
    document.getElementById('sunrise-time').textContent = formatTime(times.sunrise);
    document.getElementById('dhuhr-time').textContent = formatTime(times.dhuhr);
    document.getElementById('asr-time').textContent = formatTime(times.asr);
    document.getElementById('maghrib-time').textContent = formatTime(times.maghrib);
    document.getElementById('isha-time').textContent = formatTime(times.isha);
}

// تحديث مستمع تغيير التوقيت الصيفي/الشتوي
document.getElementById('seasonal-time')?.addEventListener('change', () => {
    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    const times = window.prayerTimes?.[currentCity] || FIXED_PRAYER_TIMES[currentCity];
    const timeFormat = document.getElementById('time-format-select')?.value || '24';
    
    if (times) {
        updatePrayerTimesDisplay(times, timeFormat);
        displayRemainingPrayerTimes();
    }
});

// تحديث دالة displayRemainingPrayerTimes لتدعم التوقيت الصيفي/الشتوي
function displayRemainingPrayerTimes() {
    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    const times = window.prayerTimes?.[currentCity] || FIXED_PRAYER_TIMES[currentCity];
    const seasonalTime = document.getElementById('seasonal-time')?.value || 'winter';
    
    if (!times) {
        console.warn('لم يتم العثور على مواقيت الصلاة');
        return;
    }

    const now = new Date();
    let currentHours = now.getHours();
    const currentMinutes = now.getMinutes();
    
    // تعديل الوقت الحالي حسب التوقيت الصيفي/الشتوي
    if (seasonalTime === 'summer') {
        currentHours = (currentHours + 1) % 24;
    }
    
    const currentTimeInMinutes = currentHours * 60 + currentMinutes;

    // تحويل مواقيت الصلاة إلى دقائق مع مراعاة التوقيت الصيفي/الشتوي
    const prayerTimesInMinutes = {};
    for (const [prayer, time] of Object.entries(times)) {
        let [hours, minutes] = time.split(':').map(Number);
        if (seasonalTime === 'summer') {
            hours = (hours + 1) % 24;
        }
        prayerTimesInMinutes[prayer] = hours * 60 + minutes;
    }

    // تحديث العرض
    const nextPrayerElement = document.querySelector('.next-prayer-text');
    if (nextPrayerElement) {
        const nextPrayer = findNextPrayer(prayerTimesInMinutes, currentTimeInMinutes);
        if (nextPrayer) {
            nextPrayerElement.textContent = `الصلاة القادمة: ${nextPrayer.arabicName}`;
        }
    }
}

// إضافة مستمع لتحديث الساعة عند تغيير التوقيت
document.getElementById('seasonal-time')?.addEventListener('change', () => {
    const city = document.getElementById('city-select')?.value || 'Asia/Amman';
    updatePrayerTimes(city);
    updateAll();
});

// حفظ إعدادات النص في التخزين المحلي
function saveTextSettings(fontSize, textColor) {
    localStorage.setItem('fontSize', fontSize);
    localStorage.setItem('textColor', textColor);
}

// استرجاع إعدادات النص عند تحميل الصفحة
function loadTextSettings() {
    const textOverlay = document.getElementById("text-overlay");
    const savedFontSize = localStorage.getItem('fontSize');
    const savedTextColor = localStorage.getItem('textColor');
    
    if (savedFontSize) {
        textOverlay.style.fontSize = savedFontSize;
    }
    if (savedTextColor) {
        textOverlay.style.color = savedTextColor;
        const textColorSelect = document.getElementById("text-color-select");
        if (textColorSelect) {
            textColorSelect.value = savedTextColor;
        }
    }
}

// إضافة مستمعي الأحداث لأزرار تغيير حجم النص
document.getElementById('increase-text')?.addEventListener('click', () => {
    const textOverlay = document.getElementById('text-overlay');
    const currentSize = parseFloat(getComputedStyle(textOverlay).fontSize);
    const newSize = (currentSize * 1.2) + 'px';
    textOverlay.style.fontSize = newSize;
    saveTextSettings(newSize, textOverlay.style.color);
});

document.getElementById('decrease-text')?.addEventListener('click', () => {
    const textOverlay = document.getElementById('text-overlay');
    const currentSize = parseFloat(getComputedStyle(textOverlay).fontSize);
    const newSize = (currentSize * 0.8) + 'px';
    textOverlay.style.fontSize = newSize;
    saveTextSettings(newSize, textOverlay.style.color);
});

// مستمع الحدث لتغيير لون النص
document.getElementById('text-color-select')?.addEventListener('change', (e) => {
    const textOverlay = document.getElementById('text-overlay');
    textOverlay.style.color = e.target.value;
    saveTextSettings(textOverlay.style.fontSize, e.target.value);
});

// إضافة عناصر اختيار المذهب وطريقة الحساب في HTML
function addCalculationOptions() {
    const settingsMenu = document.querySelector(".settings-menu");
    
    // إضافة اختيار طريقة الحساب
    const methodDiv = document.createElement("div");
    methodDiv.className = "setting-item";
    methodDiv.innerHTML = `
        <label for="calculation-method">طريقة الحساب:</label>
        <select id="calculation-method">
            <option value="standard">الطريقة القياسية</option>
            <option value="umAlQura">أم القرى</option>
        </select>
    `;
    
    // إضافة اختيار المذهب
    const juristicDiv = document.createElement("div");
    juristicDiv.className = "setting-item";
    juristicDiv.innerHTML = `
        <label for="juristic-method">المذهب:</label>
        <select id="juristic-method">
            <option value="shafi">الشافعي</option>
            <option value="hanafi">الحنفي</option>
        </select>
    `;
    
    settingsMenu.appendChild(methodDiv);
    settingsMenu.appendChild(juristicDiv);
    
    // إضافة مستمعي الأحداث
    document.getElementById("calculation-method").addEventListener("change", () => {
        updateAll();
    });
    
    document.getElementById("juristic-method").addEventListener("change", () => {
        updateAll();
    });
}

// تحديث العد التنازلي للصلوات
function updatePrayerCountdowns() {
    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    const times = FIXED_PRAYER_TIMES[currentCity];
    if (!times) {
        console.error(`مواقيت الصلاة غير متوفرة لمدينة ${currentCity}`);
        return;
    }

    const now = new Date();
    const currentHours = now.getHours();
    const currentMinutes = now.getMinutes();
    const currentSeconds = now.getSeconds();
    const currentTimeInMinutes = (currentHours * 60) + currentMinutes;

    const prayers = [
        { name: 'fajr', time: times.fajr, duration: 30, arabicName: 'الفجر' },
        { name: 'dhuhr', time: times.dhuhr, duration: 15, arabicName: 'الظهر' },
        { name: 'asr', time: times.asr, duration: 15, arabicName: 'العصر' },
        { name: 'maghrib', time: times.maghrib, duration: 10, arabicName: 'المغرب' },
        { name: 'isha', time: times.isha, duration: 15, arabicName: 'العشاء' }
    ];

    const countdownTime = document.querySelector('.countdown-time');
    const countdownLabel = document.querySelector('.next-prayer-text');
    let isCountingDown = false;

    for (const prayer of prayers) {
        const [hours, minutes] = prayer.time.split(':').map(Number);
        const prayerTimeInMinutes = hours * 60 + minutes;
        
        // التحقق من وقت الأذان بالضبط
        if (currentTimeInMinutes === prayerTimeInMinutes && currentSeconds === 0) {
            // تشغيل الأذان
            const adhanEnabled = document.getElementById('enable-adhan')?.checked;
            if (adhanEnabled) {
                const adhanAudio = document.getElementById('adhan-audio');
                if (adhanAudio) {
                    adhanAudio.src = document.getElementById('adhan-sound').value;
                    adhanAudio.play().catch(error => {
                        console.warn('يرجى النقر على الصفحة لتفعيل تشغيل الصوت', error);
                        // يمكنك الانتظار حتى يتم التفاعل لاحقًا
                    });
                }
            }
            // بدء العد التنازلي للإقامة
            countdownLabel.textContent = `الوقت المتبقي لإقامة ${prayer.arabicName}`;
            document.querySelector('.countdown-circle').classList.add('active');
            isCountingDown = true;
        }

        // العد التنازلي للإقامة
        if (currentTimeInMinutes >= prayerTimeInMinutes && 
            currentTimeInMinutes < prayerTimeInMinutes + prayer.duration) {
            
            isCountingDown = true;
            const remainingMinutes = prayer.duration - (currentTimeInMinutes - prayerTimeInMinutes) - 1;
            const remainingSeconds = 60 - currentSeconds;

            countdownTime.textContent = `${String(remainingMinutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
            countdownLabel.textContent = `الوقت المتبقي لإقامة ${prayer.arabicName}`;
            break;
        }
    }

    if (!isCountingDown && countdownTime) {
        countdownTime.textContent = '00:00';
        countdownLabel.textContent = 'الصلاة القادمة';
        document.querySelector('.countdown-circle').classList.remove('active');
    }
}

// تحديث العد التنازلي كل ثانية
setInterval(updatePrayerCountdowns, 1000);

// تحديث كائن FIXED_PRAYER_TIMES ليشمل مدينة الرياض
const FIXED_PRAYER_TIMES = {
    'Asia/Amman': {
        fajr: '05:50',
        sunrise: '07:11',
        dhuhr: '12:49',
        asr: '16:01',
        maghrib: '18:31',
        isha: '19:48'
    },
    'Asia/Dubai': {
        fajr: '05:30',
        sunrise: '07:00',
        dhuhr: '12:30',
        asr: '15:30',
        maghrib: '18:30',
        isha: '20:00'
    },
    'Asia/Riyadh': {  // إضافة مواقيت الرياض
        fajr: '05:24',
        sunrise: '06:39',
        dhuhr: '12:24',
        asr: '15:45',
        maghrib: '18:09',
        isha: '19:24'
    }
};

// إضافة دالة مساعدة لإضافة أو طرح دقائق من الوقت
function addOffsetToTime(time, offsetMinutes) {
    // تحويل الوقت إلى دقائق
    const [hours, minutes] = time.split(':').map(Number);
    let totalMinutes = hours * 60 + minutes + offsetMinutes;
    
    // التأكد من أن الوقت في نطاق 24 ساعة
    totalMinutes = ((totalMinutes % (24 * 60)) + (24 * 60)) % (24 * 60);
    
    // تحويل الدقائق مرة أخرى إلى ساعات ودقائق
    const newHours = Math.floor(totalMinutes / 60);
    const newMinutes = totalMinutes % 60;
    
    // إرجاع الوقت بتنسيق HH:MM
    return `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
}

// تحديث مواقيت الصلاة كل يوم عند منتصف الليل
function setupDailyUpdate() {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const timeUntilMidnight = tomorrow - now;
    
    setTimeout(() => {
        const city = localStorage.getItem('selectedCity') || 'Asia/Amman';
        updatePrayerTimes(city);
        // إعداد التحديث التالي
        setupDailyUpdate();
    }, timeUntilMidnight);
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    updatePrayerTimes(savedCity);
    setupDailyUpdate();
});

// إضافة دالة findNextPrayer
function findNextPrayer(prayerTimesInMinutes, currentTimeInMinutes) {
    const prayerNames = {
        fajr: 'الفجر',
        sunrise: 'الشروق',
        dhuhr: 'الظهر',
        asr: 'العصر',
        maghrib: 'المغرب',
        isha: 'العشاء'
    };

    let nextPrayer = null;
    let minDifference = Infinity;

    for (const [prayer, timeInMinutes] of Object.entries(prayerTimesInMinutes)) {
        let difference = timeInMinutes - currentTimeInMinutes;
        
        // إذا كان الوقت قد مر، نضيف 24 ساعة
        if (difference < 0) {
            difference += 24 * 60;
        }
        
        if (difference < minDifference) {
            minDifference = difference;
            nextPrayer = {
                name: prayer,
                arabicName: prayerNames[prayer],
                remainingTime: difference
            };
        }
    }
    
    return nextPrayer;
}

// تحديث دالة setupPrayerTimeUpdates لتكون أكثر تنظيماً
function setupPrayerTimeUpdates() {
    // تحديث عرض المواقيت كل ثانية
    setInterval(() => {
        const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
        const times = window.prayerTimes?.[currentCity] || FIXED_PRAYER_TIMES[currentCity];
        
        if (times) {
            const timeFormat = document.getElementById('time-format-select')?.value || '24';
            updatePrayerTimesDisplay(times, timeFormat);
            displayRemainingPrayerTimes();
            checkCurrentPrayerTime(); // إضافة فحص وقت الصلاة
        }
    }, 1000);

    // تحديث المواقيت عند منتصف الليل
    function setupMidnightUpdate() {
        const now = new Date();
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);
        
        const timeUntilMidnight = tomorrow - now;
        
        setTimeout(async () => {
            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
            await updatePrayerTimes(currentCity);
            setupMidnightUpdate(); // إعداد التحديث التالي
        }, timeUntilMidnight);
    }

    // بدء التحديث عند منتصف الليل
    setupMidnightUpdate();

    // تحديث من API كل ساعة
    setInterval(async () => {
        const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
        await updatePrayerTimes(currentCity);
    }, 60 * 60 * 1000);
}

// تحديث مستمع تحميل الصفحة
document.addEventListener('DOMContentLoaded', async () => {
    updateCityList();
    const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    await updatePrayerTimes(savedCity);
    setupPrayerTimeUpdates(); // استدعاء دالة التحديث الموحدة فقط
    
    // إضافة مستمع تغيير المدينة
    const citySelect = document.getElementById('city-select');
    if (citySelect) {
        citySelect.addEventListener('change', async (e) => {
            const selectedCity = e.target.value;
            localStorage.setItem('selectedCity', selectedCity);
            await updatePrayerTimes(selectedCity);
        });
    }
});

// تحديث دالة updateWeather لتكون أكثر مرونة مع الأخطاء
async function updateWeather() {
    try {
        const response = await fetch('https://api.aladhan.com/v1/timingsByCity?city=Dubai&country=UAE&method=8');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        
        // تحديث العرض
        const weatherDisplay = document.querySelector('.weather-display');
        if (weatherDisplay) {
            const temperatureDiv = weatherDisplay.querySelector('.temperature');
            const descriptionDiv = weatherDisplay.querySelector('.description');
            
            if (data.data?.temperature) {
                temperatureDiv.textContent = `${Math.round(data.data.temperature)}°C`;
            } else {
                temperatureDiv.textContent = '--°C';
            }
            
            descriptionDiv.textContent = 'تم تحديث البيانات';
        }
    } catch (error) {
        console.warn('خطأ في تحديث الطقس:', error);
        // استخدام البيانات المخزنة محلياً
        const cachedWeather = localStorage.getItem('weatherData');
        if (cachedWeather) {
            const weatherData = JSON.parse(cachedWeather);
            updateWeatherDisplay(weatherData);
        }
    }
}

// تحديث دالة لتشغيل الأذان وبدء العد التنازلي
function playAdhanAndStartCountdown(prayer) {
    // تشغيل الأذان
    playAdhan();
    
    // بدء العد التنازلي للإقامة
    const durations = JSON.parse(localStorage.getItem('iqama_durations')) || {
        fajr: 15,
        dhuhr: 15,
        asr: 15,
        maghrib: 10,
        isha: 15
    };
    
    const duration = durations[prayer.toLowerCase()] || 15;
    startIqamahCountdown(prayer, duration);
}

// بدء العد التنازلي للإقامة
function startIqamahCountdown(prayer, duration) {
    const countdownElement = document.getElementById('iqamah-countdown');
    const labelElement = document.getElementById('iqamah-label');
    const timeElement = document.getElementById('iqamah-time');
    const prayerNameElement = document.getElementById('iqamah-prayer-name');
    
    // إظهار العد التنازلي في منتصف الشاشة
    countdownElement.style.display = 'block';
    labelElement.textContent = 'متبقي للإقامة';
    prayerNameElement.textContent = prayer;
    
    let remainingMinutes = duration;
    let remainingSeconds = 0;
    
    function updateCountdown() {
        if (remainingMinutes === 0 && remainingSeconds === 0) {
            clearInterval(countdownTimer);
            countdownElement.style.display = 'none';
            // بدء التعتيم بعد انتهاء العد التنازلي
            dimScreen(prayer);
            return;
        }
        
        if (remainingSeconds === 0) {
            remainingMinutes--;
            remainingSeconds = 59;
        } else {
            remainingSeconds--;
        }
        
        timeElement.textContent = `${String(remainingMinutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
    }
    
    const countdownTimer = setInterval(updateCountdown, 1000);
    updateCountdown();
}

// تعتيم الشاشة
function dimScreen(prayer) {
    if (isScreenDimmed) return;
    
    isScreenDimmed = true;
    const dimOverlay = document.getElementById('dim-overlay');
    const dimCountdown = document.getElementById('dim-countdown');
    const dimPrayerName = document.getElementById('dim-prayer-name');
    
    // إعداد التعتيم
    dimOverlay.style.display = 'flex';
    dimPrayerName.textContent = prayer;
    
    // الحصول على مدة التعتيم المحفوظة
    const darknessDurations = JSON.parse(localStorage.getItem('darkness_durations')) || {
        fajr: 15,
        dhuhr: 15,
        asr: 15,
        maghrib: 15,
        isha: 15
    };
    
    const duration = darknessDurations[prayer.toLowerCase()] || 15;
    dimmingDuration = duration;
    dimmingStartTime = new Date();
    
    // بدء العد التنازلي للتعتيم
    updateDimCountdown();
    dimmingTimer = setInterval(updateDimCountdown, 1000);
}

// تحديث العد التنازلي للتعتيم
function updateDimCountdown() {
    const now = new Date();
    const elapsedSeconds = Math.floor((now - dimmingStartTime) / 1000);
    const remainingSeconds = dimmingDuration * 60 - elapsedSeconds;
    
    if (remainingSeconds <= 0) {
        clearInterval(dimmingTimer);
        clearDimScreen();
        return;
    }
    
    const minutes = Math.floor(remainingSeconds / 60);
    const seconds = remainingSeconds % 60;
    document.getElementById('dim-countdown').textContent = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    
    // إذا انتهى العد التنازلي، عرض الساعة الرقمية
    if (minutes === 0 && seconds === 0) {
        showDigitalClock();
    }
}

// عرض الساعة الرقمية في منتصف الشاشة
function showDigitalClock() {
    const dimOverlay = document.getElementById('dim-overlay');
    const digitalClock = document.createElement('div');
    digitalClock.id = 'dim-digital-clock';
    digitalClock.style.cssText = `
        font-size: 72px;
        color: white;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        margin-top: 20px;
    `;
    
    dimOverlay.appendChild(digitalClock);
    updateDimDigitalClock();
    digitalClockTimer = setInterval(updateDimDigitalClock, 1000);
}

// تحديث الساعة الرقمية
function updateDimDigitalClock() {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    
    const digitalClock = document.getElementById('dim-digital-clock');
    if (digitalClock) {
        digitalClock.textContent = `${hours}:${minutes}:${seconds}`;
    }
}

// إزالة التعتيم
function clearDimScreen() {
    isScreenDimmed = false;
    const dimOverlay = document.getElementById('dim-overlay');
    const digitalClock = document.getElementById('dim-digital-clock');
    
    if (digitalClock) {
        digitalClock.remove();
    }
    
    clearInterval(dimmingTimer);
    clearInterval(digitalClockTimer);
    dimOverlay.style.display = 'none';
}

// تعديل دالة checkCurrentPrayerTime
function checkCurrentPrayerTime() {
    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    const times = window.prayerTimes?.[currentCity] || FIXED_PRAYER_TIMES[currentCity];
    const adhanEnabled = document.getElementById('enable-adhan')?.checked;
    
    if (!times) {
        console.warn('لم يتم العثور على مواقيت الصلاة');
        return;
    }

    const now = new Date();
    const currentHours = now.getHours();
    const currentMinutes = now.getMinutes();
    const currentSeconds = now.getSeconds();
    const currentTime = `${String(currentHours).padStart(2, '0')}:${String(currentMinutes).padStart(2, '0')}`;

    // تعريف الصلوات مع صوت الأذان المخصص لكل صلاة
    const prayers = {
        fajr: { 
            time: times.fajr, 
            name: 'الفجر',
            audio: 'audio/audio_fajr.mp3',
            duration: parseInt(document.getElementById('fajr-iqama-duration').value) || 15
        },
        dhuhr: { 
            time: times.dhuhr, 
            name: 'الظهر',
            audio: 'audio/audio_dhar.mp3',
            duration: parseInt(document.getElementById('dhuhr-iqama-duration').value) || 15
        },
        asr: { 
            time: times.asr, 
            name: 'العصر',
            audio: 'audio/audio_dhar.mp3',
            duration: parseInt(document.getElementById('asr-iqama-duration').value) || 15
        },
        maghrib: { 
            time: times.maghrib, 
            name: 'المغرب',
            audio: 'audio/audio_dhar.mp3',
            duration: parseInt(document.getElementById('maghrib-iqama-duration').value) || 10
        },
        isha: { 
            time: times.isha, 
            name: 'العشاء',
            audio: 'audio/audio_dhar.mp3',
            duration: parseInt(document.getElementById('isha-iqama-duration').value) || 15
        }
    };

    for (const [key, prayer] of Object.entries(prayers)) {
        // التحقق من تطابق الوقت الحالي مع وقت الصلاة
        if (prayer.time === currentTime && currentSeconds === 0) {
            console.log(`حان وقت صلاة ${prayer.name}`);
            
            // التحقق من تفعيل الأذان
            if (adhanEnabled) {
                const adhanAudio = document.getElementById('adhan-audio');
                adhanAudio.src = prayer.audio;
                
                // تشغيل الأذان
                adhanAudio.play().catch(error => {
                    console.warn('فشل في تشغيل الأذان:', error);
                });

                // تعتيم الشاشة بعد انتهاء الأذان
                adhanAudio.onended = () => {
                    dimmingDuration = prayer.duration;
                    document.getElementById('dim-prayer-name').textContent = `صلاة ${prayer.name}`;
                    dimScreen(prayer.name);
                };

                // عرض إشعار
                if (Notification.permission === "granted") {
                    new Notification(`حان وقت صلاة ${prayer.name}`, {
                        body: 'حي على الصلاة'
                    });
                }
            }
            break;
        }
    }
}

// إضافة دالة لحفظ مدة الإقامة
function saveIqamaDurations() {
    const durations = {
        fajr: parseInt(document.getElementById('fajr-iqama-duration').value) || 15,
        dhuhr: parseInt(document.getElementById('dhuhr-iqama-duration').value) || 15,
        asr: parseInt(document.getElementById('asr-iqama-duration').value) || 15,
        maghrib: parseInt(document.getElementById('maghrib-iqama-duration').value) || 10,
        isha: parseInt(document.getElementById('isha-iqama-duration').value) || 15
    };
    
    // حفظ المدد في التخزين المحلي
    localStorage.setItem('iqama_durations', JSON.stringify(durations));
    
    // إظهار رسالة نجاح
    alert('تم حفظ مدة الإقامة بنجاح');
}

// إضافة دالة لتحميل مدة الإقامة المحفوظة
function loadIqamaDurations() {
    const savedDurations = JSON.parse(localStorage.getItem('iqama_durations')) || {};
    
    // تطبيق المدد المحفوظة على حقول الإدخال
    Object.keys(savedDurations).forEach(prayer => {
        const input = document.getElementById(`${prayer}-iqama-duration`);
        if (input) {
            input.value = savedDurations[prayer];
        }
    });
}
