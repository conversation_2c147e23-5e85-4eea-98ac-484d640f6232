<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التعتيم باستخدام CSS</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        
        h1, h2 {
            color: #333;
            text-align: center;
        }
        
        .prayer-times {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            margin: 20px 0;
        }
        
        .prayer-time {
            text-align: center;
            margin: 10px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            width: 120px;
        }
        
        .prayer-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .prayer-hour {
            font-size: 1.2em;
            color: #4a3b3b;
        }
        
        .settings {
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        
        .settings h2 {
            margin-top: 0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        select, input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        button {
            background-color: #4a3b3b;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
        }
        
        button:hover {
            background-color: #71d3ee;
        }
        
        .next-prayer {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9f7fe;
            border-radius: 5px;
            text-align: center;
        }
        
        .next-prayer h2 {
            margin-top: 0;
            color: #0078d4;
        }
        
        .test-buttons {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار نظام التعتيم باستخدام CSS</h1>
        
        <div class="next-prayer" id="next-prayer-container">
            <h2>الصلاة القادمة</h2>
            <div id="next-prayer-name" class="prayer-name">-</div>
            <div id="next-prayer-time" class="prayer-hour">-</div>
        </div>
        
        <div class="prayer-times" id="prayer-times-container">
            <!-- سيتم إنشاء هذا القسم ديناميكيًا -->
        </div>
        
        <div class="settings">
            <h2>إعدادات التعتيم</h2>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="enable-darkness" checked> تفعيل التعتيم
                </label>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="enable-adhan" checked> تفعيل الأذان
                </label>
            </div>
            
            <div class="form-group">
                <label for="iqama-minutes">مدة الإقامة (بالدقائق):</label>
                <input type="number" id="iqama-minutes" min="1" max="30" value="10">
            </div>
            
            <div class="form-group">
                <label for="darkness-duration-fajr">مدة التعتيم للفجر (بالدقائق):</label>
                <input type="number" id="darkness-duration-fajr" min="1" max="60" value="10">
            </div>
            
            <div class="form-group">
                <label for="darkness-duration-dhuhr">مدة التعتيم للظهر (بالدقائق):</label>
                <input type="number" id="darkness-duration-dhuhr" min="1" max="60" value="10">
            </div>
            
            <div class="form-group">
                <label for="darkness-duration-asr">مدة التعتيم للعصر (بالدقائق):</label>
                <input type="number" id="darkness-duration-asr" min="1" max="60" value="10">
            </div>
            
            <div class="form-group">
                <label for="darkness-duration-maghrib">مدة التعتيم للمغرب (بالدقائق):</label>
                <input type="number" id="darkness-duration-maghrib" min="1" max="60" value="10">
            </div>
            
            <div class="form-group">
                <label for="darkness-duration-isha">مدة التعتيم للعشاء (بالدقائق):</label>
                <input type="number" id="darkness-duration-isha" min="1" max="60" value="10">
            </div>
            
            <button id="save-settings">حفظ الإعدادات</button>
        </div>
        
        <div class="test-buttons">
            <button id="test-fajr">اختبار الفجر</button>
            <button id="test-dhuhr">اختبار الظهر</button>
            <button id="test-asr">اختبار العصر</button>
            <button id="test-maghrib">اختبار المغرب</button>
            <button id="test-isha">اختبار العشاء</button>
        </div>
    </div>

    <!-- عناصر الصوت -->
    <audio id="adhan-audio" preload="auto"></audio>

    <!-- استدعاء ملفات النظام -->
    <script src="prayer-times-accurate.js"></script>
    <script src="prayer-manager.js"></script>
    <script src="prayer-darkness-css.js"></script>
    
    <script>
        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة نظام مواقيت الصلاة
            PrayerManager.initialize();
            
            // تهيئة نظام التعتيم باستخدام CSS
            CSSDarknessSystem.initialize();
            
            // عرض مواقيت الصلاة
            displayPrayerTimes();
            
            // عرض الصلاة القادمة
            displayNextPrayer();
            
            // إعداد مستمعي الأحداث
            setupEventListeners();
            
            // تحديث مواقيت الصلاة كل دقيقة
            setInterval(function() {
                displayNextPrayer();
            }, 60 * 1000);
        });
        
        // عرض مواقيت الصلاة
        function displayPrayerTimes() {
            const cityName = 'عمان'; // استخدام عمان كمدينة افتراضية
            const times = PrayerManager.getPrayerTimes(cityName);
            
            if (!times) {
                console.error(`لم يتم العثور على مواقيت لمدينة ${cityName}`);
                return;
            }
            
            const container = document.getElementById('prayer-times-container');
            container.innerHTML = '';
            
            const prayers = [
                { name: 'fajr', arabicName: 'الفجر' },
                { name: 'sunrise', arabicName: 'الشروق' },
                { name: 'dhuhr', arabicName: 'الظهر' },
                { name: 'asr', arabicName: 'العصر' },
                { name: 'maghrib', arabicName: 'المغرب' },
                { name: 'isha', arabicName: 'العشاء' }
            ];
            
            for (const prayer of prayers) {
                const div = document.createElement('div');
                div.className = 'prayer-time';
                
                const nameDiv = document.createElement('div');
                nameDiv.className = 'prayer-name';
                nameDiv.textContent = prayer.arabicName;
                
                const timeDiv = document.createElement('div');
                timeDiv.className = 'prayer-hour';
                timeDiv.textContent = times[prayer.name];
                
                div.appendChild(nameDiv);
                div.appendChild(timeDiv);
                container.appendChild(div);
            }
        }
        
        // عرض الصلاة القادمة
        function displayNextPrayer() {
            const cityName = 'عمان'; // استخدام عمان كمدينة افتراضية
            const nextPrayer = PrayerManager.getNextPrayer(cityName);
            
            if (!nextPrayer) {
                console.error(`لم يتم العثور على الصلاة القادمة لمدينة ${cityName}`);
                return;
            }
            
            document.getElementById('next-prayer-name').textContent = nextPrayer.arabicName;
            document.getElementById('next-prayer-time').textContent = nextPrayer.time;
        }
        
        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // حفظ الإعدادات
            document.getElementById('save-settings').addEventListener('click', function() {
                // تحديث إعدادات التعتيم
                PrayerManager.settings.darknessEnabled = document.getElementById('enable-darkness').checked;
                PrayerManager.settings.adhanEnabled = document.getElementById('enable-adhan').checked;
                
                // تحديث مدة الإقامة
                CSSDarknessSystem.iqamaMinutes = parseInt(document.getElementById('iqama-minutes').value) || 10;
                
                // تحديث مدة التعتيم
                PrayerManager.darknessDurations = PrayerManager.darknessDurations || {};
                PrayerManager.darknessDurations.fajr = parseInt(document.getElementById('darkness-duration-fajr').value) || 10;
                PrayerManager.darknessDurations.dhuhr = parseInt(document.getElementById('darkness-duration-dhuhr').value) || 10;
                PrayerManager.darknessDurations.asr = parseInt(document.getElementById('darkness-duration-asr').value) || 10;
                PrayerManager.darknessDurations.maghrib = parseInt(document.getElementById('darkness-duration-maghrib').value) || 10;
                PrayerManager.darknessDurations.isha = parseInt(document.getElementById('darkness-duration-isha').value) || 10;
                
                // حفظ الإعدادات
                PrayerManager.saveSettings();
                if (typeof PrayerManager.saveDarknessDurations === 'function') {
                    PrayerManager.saveDarknessDurations();
                }
                
                alert('تم حفظ الإعدادات بنجاح');
            });
            
            // اختبار الفجر
            document.getElementById('test-fajr').addEventListener('click', function() {
                testPrayer('fajr', 'الفجر');
            });
            
            // اختبار الظهر
            document.getElementById('test-dhuhr').addEventListener('click', function() {
                testPrayer('dhuhr', 'الظهر');
            });
            
            // اختبار العصر
            document.getElementById('test-asr').addEventListener('click', function() {
                testPrayer('asr', 'العصر');
            });
            
            // اختبار المغرب
            document.getElementById('test-maghrib').addEventListener('click', function() {
                testPrayer('maghrib', 'المغرب');
            });
            
            // اختبار العشاء
            document.getElementById('test-isha').addEventListener('click', function() {
                testPrayer('isha', 'العشاء');
            });
        }
        
        // اختبار صلاة
        function testPrayer(prayerName, arabicName) {
            // الحصول على مواقيت الصلاة
            const cityName = 'عمان'; // استخدام عمان كمدينة افتراضية
            const times = PrayerManager.getPrayerTimes(cityName);
            
            if (!times) {
                console.error(`لم يتم العثور على مواقيت لمدينة ${cityName}`);
                return;
            }
            
            // إنشاء كائن الصلاة
            const prayer = {
                name: prayerName,
                arabicName: arabicName,
                time: times[prayerName]
            };
            
            // تشغيل الأذان إذا كان مفعلاً
            if (PrayerManager.settings.adhanEnabled) {
                CSSDarknessSystem.playAdhan();
            }
            
            // بدء العد التنازلي للإقامة
            CSSDarknessSystem.startIqamaCountdown(prayer);
        }
    </script>
</body>
</html>
