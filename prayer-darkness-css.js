/**
 * نظام التعتيم باستخدام CSS فقط
 * يقوم بتشغيل الأذان وتعتيم الشاشة وعرض العد التنازلي للإقامة
 */

// نظام التعتيم باستخدام CSS
const CSSDarknessSystem = {
    // المتغيرات العامة
    isActive: false,
    currentPrayer: null,
    countdownInterval: null,
    darknessInterval: null,
    darknessTimeout: null,
    iqamaMinutes: 10, // مدة الإقامة الافتراضية (10 دقائق)
    
    // تهيئة النظام
    initialize: function() {
        console.log('تهيئة نظام التعتيم باستخدام CSS...');
        
        // إضافة أنماط CSS
        this.addStyles();
        
        // إنشاء عناصر التعتيم
        this.createDarknessElements();
        
        // إعداد مراقبة وقت الصلاة
        this.setupPrayerTimeMonitoring();
        
        console.log('تم تهيئة نظام التعتيم باستخدام CSS بنجاح');
    },
    
    // إضافة أنماط CSS
    addStyles: function() {
        // إنشاء عنصر style
        const style = document.createElement('style');
        style.textContent = `
            .css-darkness-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.9);
                z-index: 9999;
                display: none;
            }
            
            .css-darkness-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                text-align: center;
                color: white;
                width: 80%;
            }
            
            .css-prayer-title {
                font-size: 2rem;
                margin: 0 0 20px 0;
                padding: 0;
                line-height: 1;
                font-family: Arial, sans-serif;
            }
            
            .css-prayer-countdown {
                font-size: 1.5rem;
                margin: 0 0 20px 0;
                padding: 0;
                line-height: 1;
                font-family: Arial, sans-serif;
            }
            
            .css-prayer-clock {
                font-size: 3rem;
                margin: 0;
                padding: 0;
                line-height: 1;
                font-family: Arial, sans-serif;
            }
        `;
        
        // إضافة عنصر style إلى الصفحة
        document.head.appendChild(style);
    },
    
    // إنشاء عناصر التعتيم
    createDarknessElements: function() {
        // إنشاء طبقة التعتيم
        const overlay = document.createElement('div');
        overlay.className = 'css-darkness-overlay';
        
        // إنشاء حاوية المحتوى
        const content = document.createElement('div');
        content.className = 'css-darkness-content';
        
        // إنشاء عنوان الصلاة
        const title = document.createElement('div');
        title.className = 'css-prayer-title';
        
        // إنشاء عرض العد التنازلي
        const countdown = document.createElement('div');
        countdown.className = 'css-prayer-countdown';
        
        // إنشاء عرض الساعة
        const clock = document.createElement('div');
        clock.className = 'css-prayer-clock';
        
        // إضافة العناصر إلى الحاوية
        content.appendChild(title);
        content.appendChild(countdown);
        content.appendChild(clock);
        
        // إضافة الحاوية إلى طبقة التعتيم
        overlay.appendChild(content);
        
        // إضافة طبقة التعتيم إلى الصفحة
        document.body.appendChild(overlay);
        
        // إضافة مستمع النقر لإغلاق التعتيم
        overlay.addEventListener('click', () => {
            this.stopDarkness();
        });
        
        // حفظ مراجع للعناصر
        this.overlay = overlay;
        this.title = title;
        this.countdown = countdown;
        this.clock = clock;
    },
    
    // إعداد مراقبة وقت الصلاة
    setupPrayerTimeMonitoring: function() {
        // التحقق من وقت الصلاة كل دقيقة
        setInterval(() => {
            this.checkPrayerTime();
        }, 60 * 1000); // كل دقيقة
        
        // التحقق الأولي
        this.checkPrayerTime();
    },
    
    // التحقق من وقت الصلاة
    checkPrayerTime: function() {
        // التحقق مما إذا كان التعتيم مفعلاً في الإعدادات
        if (!PrayerManager.settings.darknessEnabled) {
            return;
        }
        
        // الحصول على الصلاة القادمة
        const nextPrayer = window.getNextPrayer ? window.getNextPrayer() : PrayerManager.getNextPrayer();
        if (!nextPrayer) {
            return;
        }
        
        // الحصول على الوقت الحالي
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();
        
        // تحويل وقت الصلاة إلى ساعات ودقائق
        let prayerHour, prayerMinute;
        
        if (nextPrayer.time.includes('ص') || nextPrayer.time.includes('م')) {
            // إذا كان الوقت بنظام 12 ساعة
            const isPM = nextPrayer.time.includes('م');
            const timeParts = nextPrayer.time.replace(/[صم]/g, '').trim().split(':');
            prayerHour = parseInt(timeParts[0]);
            prayerMinute = parseInt(timeParts[1]);
            
            // تحويل إلى نظام 24 ساعة
            if (isPM && prayerHour < 12) prayerHour += 12;
            if (!isPM && prayerHour === 12) prayerHour = 0;
        } else {
            // إذا كان الوقت بنظام 24 ساعة
            const timeParts = nextPrayer.time.split(':');
            prayerHour = parseInt(timeParts[0]);
            prayerMinute = parseInt(timeParts[1]);
        }
        
        // التحقق مما إذا كان الوقت الحالي هو وقت الصلاة
        if (currentHour === prayerHour && currentMinute === prayerMinute) {
            // تجنب تكرار التعتيم لنفس الصلاة
            if (this.currentPrayer !== nextPrayer.name) {
                this.currentPrayer = nextPrayer.name;
                
                // تشغيل الأذان إذا كان مفعلاً
                if (PrayerManager.settings.adhanEnabled) {
                    this.playAdhan();
                }
                
                // بدء العد التنازلي للإقامة
                this.startIqamaCountdown(nextPrayer);
            }
        }
    },
    
    // تشغيل الأذان
    playAdhan: function() {
        try {
            const adhanAudio = document.getElementById('adhan-audio');
            if (adhanAudio) {
                // تعيين مصدر الأذان
                adhanAudio.src = 'audio/adhan.mp3';
                
                // محاولة تشغيل الأذان
                const playPromise = adhanAudio.play();
                
                // التعامل مع الوعد
                if (playPromise !== undefined) {
                    playPromise.then(_ => {
                        console.log('تم تشغيل الأذان بنجاح');
                    }).catch(error => {
                        console.error('خطأ في تشغيل الأذان:', error);
                        // محاولة تشغيل الأذان بعد تفاعل المستخدم
                        alert('انقر موافق لتشغيل الأذان');
                        adhanAudio.play().catch(e => console.error('فشل تشغيل الأذان مرة أخرى:', e));
                    });
                }
            } else {
                console.error('لم يتم العثور على عنصر الأذان');
            }
        } catch (error) {
            console.error('خطأ في تشغيل الأذان:', error);
        }
    },
    
    // بدء العد التنازلي للإقامة
    startIqamaCountdown: function(prayer) {
        // تحديث مدة الإقامة من الحقل إذا كان موجودًا
        const iqamaInput = document.getElementById('iqama-minutes');
        if (iqamaInput) {
            this.iqamaMinutes = parseInt(iqamaInput.value) || 10;
        }
        
        // إظهار طبقة التعتيم
        this.overlay.style.display = 'block';
        this.isActive = true;
        
        // تعيين عنوان الصلاة
        this.title.textContent = `حان وقت صلاة ${prayer.arabicName}`;
        
        // تعيين نص العد التنازلي
        this.countdown.textContent = `الإقامة بعد ${this.iqamaMinutes}:00 دقيقة`;
        
        // تحديث الساعة
        this.updateClock();
        
        // بدء العد التنازلي
        let remainingSeconds = this.iqamaMinutes * 60;
        
        // مسح العد التنازلي السابق إذا كان موجودًا
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
        }
        
        // بدء العد التنازلي الجديد
        this.countdownInterval = setInterval(() => {
            remainingSeconds--;
            
            if (remainingSeconds <= 0) {
                // إيقاف العد التنازلي
                clearInterval(this.countdownInterval);
                
                // بدء التعتيم
                this.startDarkness(prayer);
            } else {
                // تحديث عرض العد التنازلي
                const minutes = Math.floor(remainingSeconds / 60);
                const seconds = remainingSeconds % 60;
                this.countdown.textContent = 
                    `الإقامة بعد ${minutes}:${seconds < 10 ? '0' + seconds : seconds} دقيقة`;
            }
        }, 1000);
    },
    
    // بدء التعتيم
    startDarkness: function(prayer) {
        // تحديث عرض التعتيم
        this.title.textContent = `صلاة ${prayer.arabicName}`;
        
        // تحديث مدة التعتيم من الحقل إذا كان موجودًا
        let darknessDuration = 10; // القيمة الافتراضية
        
        const darknessDurationInput = document.getElementById(`darkness-duration-${prayer.name}`);
        if (darknessDurationInput) {
            darknessDuration = parseInt(darknessDurationInput.value) || 10;
            // تحديث القيمة في PrayerManager
            if (PrayerManager && PrayerManager.darknessDurations) {
                PrayerManager.darknessDurations[prayer.name] = darknessDuration;
                // حفظ القيم
                if (typeof PrayerManager.saveDarknessDurations === 'function') {
                    PrayerManager.saveDarknessDurations();
                }
            }
        } else if (PrayerManager && PrayerManager.darknessDurations) {
            // استخدام القيمة من PrayerManager إذا كانت موجودة
            darknessDuration = PrayerManager.darknessDurations[prayer.name] || 10;
        }
        
        // تحديث نص العد التنازلي
        this.countdown.textContent = 
            `مدة التعتيم المتبقية: ${darknessDuration}:00 دقيقة`;
        
        // بدء العد التنازلي لمدة التعتيم
        let remainingSeconds = darknessDuration * 60;
        
        // مسح العد التنازلي السابق إذا كان موجودًا
        if (this.darknessInterval) {
            clearInterval(this.darknessInterval);
        }
        
        // بدء العد التنازلي الجديد
        this.darknessInterval = setInterval(() => {
            remainingSeconds--;
            
            if (remainingSeconds <= 0) {
                // إيقاف العد التنازلي
                clearInterval(this.darknessInterval);
                this.darknessInterval = null;
                
                // إيقاف التعتيم
                this.stopDarkness();
            } else {
                // تحديث عرض العد التنازلي
                const minutes = Math.floor(remainingSeconds / 60);
                const seconds = remainingSeconds % 60;
                this.countdown.textContent = 
                    `مدة التعتيم المتبقية: ${minutes}:${seconds < 10 ? '0' + seconds : seconds} دقيقة`;
            }
        }, 1000);
        
        // بدء مؤقت التعتيم الجديد (كاحتياط)
        this.darknessTimeout = setTimeout(() => {
            // إيقاف التعتيم
            this.stopDarkness();
        }, darknessDuration * 60 * 1000 + 5000); // تحويل الدقائق إلى مللي ثانية + 5 ثوانٍ إضافية
    },
    
    // إيقاف التعتيم
    stopDarkness: function() {
        // إخفاء طبقة التعتيم
        this.overlay.style.display = 'none';
        this.isActive = false;
        
        // مسح العد التنازلي للإقامة
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
        
        // مسح العد التنازلي للتعتيم
        if (this.darknessInterval) {
            clearInterval(this.darknessInterval);
            this.darknessInterval = null;
        }
        
        // مسح مؤقت التعتيم
        if (this.darknessTimeout) {
            clearTimeout(this.darknessTimeout);
            this.darknessTimeout = null;
        }
        
        // إيقاف الأذان إذا كان يعمل
        try {
            const adhanAudio = document.getElementById('adhan-audio');
            if (adhanAudio && !adhanAudio.paused) {
                adhanAudio.pause();
                adhanAudio.currentTime = 0;
            }
        } catch (error) {
            console.error('خطأ في إيقاف الأذان:', error);
        }
    },
    
    // تحديث الساعة
    updateClock: function() {
        const now = new Date();
        const hours = now.getHours();
        const minutes = now.getMinutes();
        const seconds = now.getSeconds();
        
        // تنسيق الوقت
        let timeString;
        if (PrayerManager.settings.timeFormat === '12h') {
            // نظام 12 ساعة
            const isPM = hours >= 12;
            let hours12 = hours % 12;
            if (hours12 === 0) hours12 = 12;
            
            timeString = `${hours12}:${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds} ${isPM ? 'م' : 'ص'}`;
        } else {
            // نظام 24 ساعة
            timeString = `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
        }
        
        // تحديث عرض الساعة
        this.clock.textContent = timeString;
        
        // تحديث الساعة كل ثانية
        setTimeout(() => {
            if (this.isActive) {
                this.updateClock();
            }
        }, 1000);
    }
};

// تصدير الكائن للاستخدام
window.CSSDarknessSystem = CSSDarknessSystem;
