<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التعتيم بعنصر واحد</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        h1, h2 {
            color: #333;
            text-align: center;
        }

        .prayer-times {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            margin: 20px 0;
        }

        .prayer-time {
            text-align: center;
            margin: 10px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            width: 120px;
        }

        .prayer-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .prayer-hour {
            font-size: 1.2em;
            color: #4a3b3b;
        }

        .settings {
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }

        .settings h2 {
            margin-top: 0;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        select, input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        button {
            background-color: #4a3b3b;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
        }

        button:hover {
            background-color: #71d3ee;
        }

        .next-prayer {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9f7fe;
            border-radius: 5px;
            text-align: center;
        }

        .next-prayer h2 {
            margin-top: 0;
            color: #0078d4;
        }

        .test-buttons {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار نظام التعتيم بعنصر واحد</h1>

        <div class="next-prayer" id="next-prayer-container">
            <h2>الصلاة القادمة</h2>
            <div id="next-prayer-name" class="prayer-name">-</div>
            <div id="next-prayer-time" class="prayer-hour">-</div>
        </div>

        <div class="prayer-times" id="prayer-times-container">
            <!-- سيتم إنشاء هذا القسم ديناميكيًا -->
        </div>

        <div class="settings">
            <h2>إعدادات التعتيم</h2>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="enable-darkness" checked> تفعيل التعتيم
                </label>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="enable-adhan" checked> تفعيل الأذان
                </label>
            </div>

            <div class="form-group">
                <label for="iqama-minutes">مدة الإقامة (بالدقائق) - من 1 إلى 40 دقيقة:</label>
                <div style="display: flex; gap: 10px;">
                    <input type="number" id="iqama-minutes" min="1" max="40" value="10" style="flex: 1;">
                    <button id="save-iqama-minutes" style="width: auto;">حفظ مدة الإقامة</button>
                </div>
            </div>

            <div class="form-group">
                <label for="darkness-duration-fajr">مدة التعتيم للفجر (بالدقائق):</label>
                <div style="display: flex; gap: 10px;">
                    <input type="number" id="darkness-duration-fajr" min="1" max="60" value="10" style="flex: 1;">
                    <button id="test-darkness-fajr" style="width: auto;">اختبار</button>
                </div>
            </div>

            <div class="form-group">
                <label for="darkness-duration-dhuhr">مدة التعتيم للظهر (بالدقائق):</label>
                <div style="display: flex; gap: 10px;">
                    <input type="number" id="darkness-duration-dhuhr" min="1" max="60" value="10" style="flex: 1;">
                    <button id="test-darkness-dhuhr" style="width: auto;">اختبار</button>
                </div>
            </div>

            <div class="form-group">
                <label for="darkness-duration-asr">مدة التعتيم للعصر (بالدقائق):</label>
                <div style="display: flex; gap: 10px;">
                    <input type="number" id="darkness-duration-asr" min="1" max="60" value="10" style="flex: 1;">
                    <button id="test-darkness-asr" style="width: auto;">اختبار</button>
                </div>
            </div>

            <div class="form-group">
                <label for="darkness-duration-maghrib">مدة التعتيم للمغرب (بالدقائق):</label>
                <div style="display: flex; gap: 10px;">
                    <input type="number" id="darkness-duration-maghrib" min="1" max="60" value="10" style="flex: 1;">
                    <button id="test-darkness-maghrib" style="width: auto;">اختبار</button>
                </div>
            </div>

            <div class="form-group">
                <label for="darkness-duration-isha">مدة التعتيم للعشاء (بالدقائق):</label>
                <div style="display: flex; gap: 10px;">
                    <input type="number" id="darkness-duration-isha" min="1" max="60" value="10" style="flex: 1;">
                    <button id="test-darkness-isha" style="width: auto;">اختبار</button>
                </div>
            </div>

            <button id="save-settings">حفظ الإعدادات</button>
        </div>

        <div class="test-buttons">
            <button id="test-fajr">اختبار الفجر</button>
            <button id="test-dhuhr">اختبار الظهر</button>
            <button id="test-asr">اختبار العصر</button>
            <button id="test-maghrib">اختبار المغرب</button>
            <button id="test-isha">اختبار العشاء</button>
        </div>
    </div>

    <!-- عناصر الصوت -->
    <audio id="adhan-audio" preload="auto"></audio>

    <!-- استدعاء ملفات النظام -->
    <script src="prayer-times-accurate.js"></script>
    <script src="prayer-manager.js"></script>
    <script src="prayer-darkness-single.js"></script>

    <script>
        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة نظام مواقيت الصلاة
            PrayerManager.initialize();

            // تهيئة نظام التعتيم بعنصر واحد
            SingleElementDarknessSystem.initialize();

            // تحديث حقل مدة الإقامة بالقيمة المحفوظة
            const iqamaInput = document.getElementById('iqama-minutes');
            if (iqamaInput) {
                // استخدام القيمة من PrayerManager إذا كانت موجودة
                if (PrayerManager.iqamaSettings && PrayerManager.iqamaSettings.defaultDuration) {
                    iqamaInput.value = PrayerManager.iqamaSettings.defaultDuration;
                    console.log(`تم تحديث حقل مدة الإقامة من PrayerManager: ${PrayerManager.iqamaSettings.defaultDuration} دقيقة`);
                } else {
                    // استخدام القيمة من SingleElementDarknessSystem
                    iqamaInput.value = SingleElementDarknessSystem.iqamaMinutes;
                    console.log(`تم تحديث حقل مدة الإقامة من SingleElementDarknessSystem: ${SingleElementDarknessSystem.iqamaMinutes} دقيقة`);
                }
            }

            // عرض مواقيت الصلاة
            displayPrayerTimes();

            // عرض الصلاة القادمة
            displayNextPrayer();

            // إعداد مستمعي الأحداث
            setupEventListeners();

            // تحديث مواقيت الصلاة كل دقيقة
            setInterval(function() {
                displayNextPrayer();
            }, 60 * 1000);
        });

        // عرض مواقيت الصلاة
        function displayPrayerTimes() {
            const cityName = 'عمان'; // استخدام عمان كمدينة افتراضية
            const times = PrayerManager.getPrayerTimes(cityName);

            if (!times) {
                console.error(`لم يتم العثور على مواقيت لمدينة ${cityName}`);
                return;
            }

            const container = document.getElementById('prayer-times-container');
            container.innerHTML = '';

            const prayers = [
                { name: 'fajr', arabicName: 'الفجر' },
                { name: 'sunrise', arabicName: 'الشروق' },
                { name: 'dhuhr', arabicName: 'الظهر' },
                { name: 'asr', arabicName: 'العصر' },
                { name: 'maghrib', arabicName: 'المغرب' },
                { name: 'isha', arabicName: 'العشاء' }
            ];

            for (const prayer of prayers) {
                const div = document.createElement('div');
                div.className = 'prayer-time';

                const nameDiv = document.createElement('div');
                nameDiv.className = 'prayer-name';
                nameDiv.textContent = prayer.arabicName;

                const timeDiv = document.createElement('div');
                timeDiv.className = 'prayer-hour';
                timeDiv.textContent = times[prayer.name];

                div.appendChild(nameDiv);
                div.appendChild(timeDiv);
                container.appendChild(div);
            }
        }

        // عرض الصلاة القادمة
        function displayNextPrayer() {
            const cityName = 'عمان'; // استخدام عمان كمدينة افتراضية
            const nextPrayer = PrayerManager.getNextPrayer(cityName);

            if (!nextPrayer) {
                console.error(`لم يتم العثور على الصلاة القادمة لمدينة ${cityName}`);
                return;
            }

            document.getElementById('next-prayer-name').textContent = nextPrayer.arabicName;
            document.getElementById('next-prayer-time').textContent = nextPrayer.time;
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // حفظ مدة الإقامة
            document.getElementById('save-iqama-minutes').addEventListener('click', function() {
                let minutes = parseInt(document.getElementById('iqama-minutes').value) || 10;

                // التأكد من أن مدة الإقامة ضمن الحدود المسموح بها
                if (minutes > 40) {
                    console.warn(`تم تحديد قيمة كبيرة لمدة الإقامة: ${minutes} دقيقة، سيتم تحديدها إلى 40 دقيقة`);
                    minutes = 40;
                    // تحديث حقل الإدخال
                    document.getElementById('iqama-minutes').value = minutes;
                }

                // حفظ مدة الإقامة في نظام التعتيم
                SingleElementDarknessSystem.saveIqamaMinutes(minutes);

                // حفظ مدة الإقامة في نظام إدارة مواقيت الصلاة
                PrayerManager.setIqamaDuration(minutes);

                // تحديث المتغير العام
                window.iqamaDuration = minutes;
                console.log(`تم تحديث المتغير العام iqamaDuration: ${minutes} دقيقة`);

                // عرض رسالة تأكيد
                alert(`تم حفظ مدة الإقامة: ${minutes} دقيقة`);
            });

            // اختبار مدة التعتيم للفجر
            document.getElementById('test-darkness-fajr').addEventListener('click', function() {
                SingleElementDarknessSystem.testDarknessDuration('fajr');
            });

            // اختبار مدة التعتيم للظهر
            document.getElementById('test-darkness-dhuhr').addEventListener('click', function() {
                SingleElementDarknessSystem.testDarknessDuration('dhuhr');
            });

            // اختبار مدة التعتيم للعصر
            document.getElementById('test-darkness-asr').addEventListener('click', function() {
                SingleElementDarknessSystem.testDarknessDuration('asr');
            });

            // اختبار مدة التعتيم للمغرب
            document.getElementById('test-darkness-maghrib').addEventListener('click', function() {
                SingleElementDarknessSystem.testDarknessDuration('maghrib');
            });

            // اختبار مدة التعتيم للعشاء
            document.getElementById('test-darkness-isha').addEventListener('click', function() {
                SingleElementDarknessSystem.testDarknessDuration('isha');
            });

            // دالة حفظ مدة التعتيم
            function saveDarknessDuration(prayerName, value) {
                const minutes = parseInt(value) || 10;
                console.log(`حفظ مدة التعتيم لصلاة ${prayerName}: ${minutes} دقيقة`);

                // حفظ القيمة في التخزين المحلي مباشرة
                try {
                    const localStorageKey = `darknessDuration_${prayerName}`;
                    localStorage.setItem(localStorageKey, minutes.toString());
                    console.log(`تم حفظ مدة التعتيم في التخزين المحلي (${localStorageKey}): ${minutes} دقيقة`);
                } catch (error) {
                    console.error('خطأ في حفظ مدة التعتيم في التخزين المحلي:', error);
                }

                // تحديث مدة التعتيم في PrayerManager
                if (typeof PrayerManager !== 'undefined') {
                    if (!PrayerManager.darknessDurations) {
                        PrayerManager.darknessDurations = {};
                    }

                    // تحديث القيمة
                    PrayerManager.darknessDurations[prayerName] = minutes;

                    // حفظ القيم
                    if (typeof PrayerManager.saveDarknessDurations === 'function') {
                        PrayerManager.saveDarknessDurations();
                        console.log(`تم حفظ مدة التعتيم في PrayerManager: ${minutes} دقيقة`);
                    } else {
                        // حفظ القيم في التخزين المحلي مباشرة
                        try {
                            localStorage.setItem('darknessDurations', JSON.stringify(PrayerManager.darknessDurations));
                            console.log(`تم حفظ مدة التعتيم في التخزين المحلي (darknessDurations): ${minutes} دقيقة`);
                        } catch (error) {
                            console.error('خطأ في حفظ مدة التعتيم في التخزين المحلي:', error);
                        }
                    }
                }

                // عرض رسالة تأكيد
                alert(`تم حفظ مدة التعتيم لصلاة ${getPrayerArabicName(prayerName)}: ${minutes} دقيقة`);
            }

            // دالة للحصول على الاسم العربي للصلاة
            function getPrayerArabicName(prayerName) {
                const prayerNames = {
                    'fajr': 'الفجر',
                    'dhuhr': 'الظهر',
                    'asr': 'العصر',
                    'maghrib': 'المغرب',
                    'isha': 'العشاء'
                };

                return prayerNames[prayerName] || prayerName;
            }
            }

            // حفظ مدة التعتيم للفجر
            document.getElementById('darkness-duration-fajr').addEventListener('change', function() {
                saveDarknessDuration('fajr', this.value);
            });

            // حفظ مدة التعتيم للظهر
            document.getElementById('darkness-duration-dhuhr').addEventListener('change', function() {
                saveDarknessDuration('dhuhr', this.value);
            });

            // حفظ مدة التعتيم للعصر
            document.getElementById('darkness-duration-asr').addEventListener('change', function() {
                saveDarknessDuration('asr', this.value);
            });

            // حفظ مدة التعتيم للمغرب
            document.getElementById('darkness-duration-maghrib').addEventListener('change', function() {
                saveDarknessDuration('maghrib', this.value);
            });

            // حفظ مدة التعتيم للعشاء
            document.getElementById('darkness-duration-isha').addEventListener('change', function() {
                saveDarknessDuration('isha', this.value);
            });

            // حفظ الإعدادات
            document.getElementById('save-settings').addEventListener('click', function() {
                // تحديث إعدادات التعتيم
                PrayerManager.settings.darknessEnabled = document.getElementById('enable-darkness').checked;
                PrayerManager.settings.adhanEnabled = document.getElementById('enable-adhan').checked;

                // تحديث مدة الإقامة
                SingleElementDarknessSystem.saveIqamaMinutes(parseInt(document.getElementById('iqama-minutes').value) || 10);

                // تحديث مدة التعتيم
                PrayerManager.darknessDurations = PrayerManager.darknessDurations || {};
                PrayerManager.darknessDurations.fajr = parseInt(document.getElementById('darkness-duration-fajr').value) || 10;
                PrayerManager.darknessDurations.dhuhr = parseInt(document.getElementById('darkness-duration-dhuhr').value) || 10;
                PrayerManager.darknessDurations.asr = parseInt(document.getElementById('darkness-duration-asr').value) || 10;
                PrayerManager.darknessDurations.maghrib = parseInt(document.getElementById('darkness-duration-maghrib').value) || 10;
                PrayerManager.darknessDurations.isha = parseInt(document.getElementById('darkness-duration-isha').value) || 10;

                // حفظ الإعدادات
                PrayerManager.saveSettings();
                if (typeof PrayerManager.saveDarknessDurations === 'function') {
                    PrayerManager.saveDarknessDurations();
                }

                alert('تم حفظ الإعدادات بنجاح');
            });

            // اختبار الفجر
            document.getElementById('test-fajr').addEventListener('click', function() {
                testPrayer('fajr', 'الفجر');
            });

            // اختبار الظهر
            document.getElementById('test-dhuhr').addEventListener('click', function() {
                testPrayer('dhuhr', 'الظهر');
            });

            // اختبار العصر
            document.getElementById('test-asr').addEventListener('click', function() {
                testPrayer('asr', 'العصر');
            });

            // اختبار المغرب
            document.getElementById('test-maghrib').addEventListener('click', function() {
                testPrayer('maghrib', 'المغرب');
            });

            // اختبار العشاء
            document.getElementById('test-isha').addEventListener('click', function() {
                testPrayer('isha', 'العشاء');
            });
        }

        // اختبار صلاة
        function testPrayer(prayerName, arabicName) {
            console.log(`اختبار صلاة ${arabicName}...`);

            // الحصول على مواقيت الصلاة
            const cityName = 'عمان'; // استخدام عمان كمدينة افتراضية
            const times = PrayerManager.getPrayerTimes(cityName);

            if (!times) {
                console.error(`لم يتم العثور على مواقيت لمدينة ${cityName}`);
                return;
            }

            // إنشاء كائن الصلاة
            const prayer = {
                name: prayerName,
                arabicName: arabicName,
                time: times[prayerName]
            };

            // تحديث مدة الإقامة من حقل الإدخال
            const iqamaInput = document.getElementById('iqama-minutes');
            if (iqamaInput) {
                const inputValue = iqamaInput.value.trim();
                const minutes = parseInt(inputValue);

                if (!isNaN(minutes) && minutes > 0) {
                    console.log(`قراءة مدة الإقامة من حقل الإدخال: ${minutes} دقيقة`);

                    // التأكد من أن مدة الإقامة ضمن الحدود المسموح بها
                    if (minutes > 40) {
                        console.warn(`تم تحديد قيمة كبيرة لمدة الإقامة: ${minutes} دقيقة، سيتم تحديدها إلى 40 دقيقة`);
                        minutes = 40;
                        // تحديث حقل الإدخال
                        iqamaInput.value = minutes;
                    }

                    // تحديث مدة الإقامة في نظام التعتيم
                    SingleElementDarknessSystem.iqamaMinutes = minutes;

                    // حفظ القيمة في التخزين المحلي
                    localStorage.setItem('iqamaMinutes', minutes.toString());
                    console.log(`تم حفظ مدة الإقامة في التخزين المحلي: ${minutes} دقيقة`);

                    // تحديث المتغير العام
                    window.iqamaDuration = minutes;
                    console.log(`تم تحديث المتغير العام iqamaDuration: ${minutes} دقيقة`);

                    // تحديث الإعدادات في PrayerManager
                    if (typeof PrayerManager !== 'undefined') {
                        if (!PrayerManager.iqamaSettings) {
                            PrayerManager.iqamaSettings = {};
                        }
                        PrayerManager.iqamaSettings.defaultDuration = minutes;
                        if (typeof PrayerManager.saveSettings === 'function') {
                            PrayerManager.saveSettings();
                            console.log(`تم تحديث إعدادات الإقامة في PrayerManager: ${minutes} دقيقة`);
                        }
                    }

                    // عرض رسالة تأكيد
                    alert(`تم تحديث مدة الإقامة إلى ${minutes} دقيقة`);
                } else {
                    console.error(`قيمة غير صالحة لمدة الإقامة: ${inputValue}`);
                    alert(`قيمة غير صالحة لمدة الإقامة: ${inputValue}`);
                    return;
                }
            } else {
                console.error('لم يتم العثور على حقل مدة الإقامة');
            }

            // تحديث مدة التعتيم من حقل الإدخال
            const darknessDurationInput = document.getElementById(`darkness-duration-${prayerName}`);
            if (darknessDurationInput) {
                const inputValue = darknessDurationInput.value.trim();
                const minutes = parseInt(inputValue);

                if (!isNaN(minutes) && minutes > 0) {
                    console.log(`قراءة مدة التعتيم من حقل الإدخال: ${minutes} دقيقة`);

                    // تحديث مدة التعتيم في PrayerManager
                    if (typeof PrayerManager !== 'undefined') {
                        if (!PrayerManager.darknessDurations) {
                            PrayerManager.darknessDurations = {};
                        }

                        // تحديث القيمة
                        PrayerManager.darknessDurations[prayerName] = minutes;

                        // حفظ القيم
                        if (typeof PrayerManager.saveDarknessDurations === 'function') {
                            PrayerManager.saveDarknessDurations();
                            console.log(`تم حفظ مدة التعتيم في PrayerManager: ${minutes} دقيقة`);
                        } else {
                            // حفظ القيم في التخزين المحلي مباشرة
                            try {
                                localStorage.setItem('darknessDurations', JSON.stringify(PrayerManager.darknessDurations));
                                console.log(`تم حفظ مدة التعتيم في التخزين المحلي: ${minutes} دقيقة`);
                            } catch (error) {
                                console.error('خطأ في حفظ مدة التعتيم في التخزين المحلي:', error);
                            }
                        }

                        // حفظ القيمة في التخزين المحلي مباشرة
                        try {
                            const localStorageKey = `darknessDuration_${prayerName}`;
                            localStorage.setItem(localStorageKey, minutes.toString());
                            console.log(`تم حفظ مدة التعتيم في التخزين المحلي (${localStorageKey}): ${minutes} دقيقة`);
                        } catch (error) {
                            console.error('خطأ في حفظ مدة التعتيم في التخزين المحلي:', error);
                        }

                        // تحديث المتغير العام
                        window.currentDarknessDuration = minutes;
                        console.log(`تم تحديث المتغير العام currentDarknessDuration: ${minutes} دقيقة`);

                        // عرض رسالة تأكيد
                        alert(`تم تحديث مدة التعتيم لصلاة ${arabicName} إلى ${minutes} دقيقة`);
                    }
                } else {
                    console.error(`قيمة غير صالحة لمدة التعتيم: ${inputValue}`);
                    alert(`قيمة غير صالحة لمدة التعتيم: ${inputValue}`);
                }
            } else {
                console.error(`لم يتم العثور على حقل مدة التعتيم لصلاة ${arabicName}`);
            }

            // تشغيل الأذان إذا كان مفعلاً
            if (PrayerManager.settings.adhanEnabled) {
                SingleElementDarknessSystem.playAdhan();
            }

            // بدء العد التنازلي للإقامة
            SingleElementDarknessSystem.startIqamaCountdown(prayer);
        }
    </script>
</body>
</html>
