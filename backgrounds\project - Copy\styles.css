.location-settings {
    margin-bottom: 20px;
}

.location-settings .setting-item {
    margin-bottom: 0.5rem;
}

.settings-select {
    width: 100%;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 1rem;
}

.settings-select:hover {
    border-color: #666;
}

.settings-select:focus {
    outline: none;
    border-color: #333;
    box-shadow: 0 0 3px rgba(0,0,0,0.2);
}

/* تنسيق حاوية المنطقة الزمنية */
.setting-item {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
}

/* تنسيق تسمية المنطقة الزمنية */
.setting-item label {
    display: block;
    color: white;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

/* تنسيق القائمة المنسدلة للمنطقة الزمنية */
#timezone {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    font-size: 1rem;
    width: 100%;
    margin-top: 4px;
}

#timezone:focus {
    border-color: #666;
    outline: none;
    box-shadow: 0 0 3px rgba(0,0,0,0.1);
}

/* تنسيق حاوية المنطقة الزمنية */
.timezone-container {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.timezone-container label {
    color: #fff;
    font-weight: bold;
    margin-bottom: 8px;
    display: block;
}

.timezone-container select {
    width: 100%;
    padding: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    border-radius: 4px;
}

/* تنسيق حالة الخطأ في تحميل الخلفية */
.background-error {
    background: linear-gradient(45deg, #2c3e50, #3498db);
}

/* تنسيق للخلفية الاحتياطية */
.background-fallback {
    background: linear-gradient(to right, #2c3e50, #3498db);
}

/* إضافة الخلفية الافتراضية */
body {
    background-image: url('../images/background1.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #2c3e50; /* خلفية احتياطية */
}

/* أنماط احتياطية للخلفية */
body {
    background-color: #2c3e50;
    background-image: url('images/default-background.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.background-fallback {
    background: linear-gradient(45deg, #2c3e50, #3498db);
}

/* تنسيق للخلفية الافتراضية */
body {
    background: #2c3e50;
    background-image: url('images/default-background.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transition: background-image 0.3s ease;
}

/* تنسيق للخلفية الاحتياطية */
.background-fallback {
    background: linear-gradient(45deg, #2c3e50, #3498db);
}

/* أنماط لحالة تحميل الصور */
.background-loading {
    background: linear-gradient(to right, #2c3e50, #3498db);
    transition: background-image 0.3s ease;
}

/* تنسيق الخلفية الافتراضية */
body {
    background: #2c3e50;
    transition: background-image 0.3s ease;
}

/* تنسيق للخلفية الاحتياطية */
.background-fallback {
    background: linear-gradient(45deg, #2c3e50, #3498db);
}

/* حذف التعريفات المكررة للخلفية */

.settings-menu {
    position: fixed;
    top: 60px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    padding: 20px;
    border-radius: 10px;
    z-index: 1000;
    display: none;
}

.settings-menu.active {
    display: block;
}

/* تحديث محددات CSS لتطابق الـ IDs الجديدة */
#city-select-main,
#timezone-select-main {
    width: 100%;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 1rem;
}
