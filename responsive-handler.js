/**
 * نظام الاستجابة للمنصات المختلفة
 * يتعامل مع التخطيطات المختلفة للأجهزة والاتجاهات
 */

const ResponsiveHandler = {
    // الإعدادات الحالية
    currentLayout: 'desktop',
    currentOrientation: 'landscape',

    // تهيئة النظام
    initialize: function() {
        console.log('تهيئة نظام الاستجابة...');

        this.setupResponsiveStyles();
        this.setupEventListeners();
        this.applyInitialLayout();

        console.log('تم تهيئة نظام الاستجابة بنجاح');
    },

    // إعداد الأنماط المتجاوبة
    setupResponsiveStyles: function() {
        const style = document.createElement('style');
        style.id = 'responsive-styles';
        style.textContent = `
            /* أنماط التلفاز */
            .tv-layout {
                font-size: 1.5em !important;
            }

            .tv-layout .vertical-panel {
                width: 8cm !important;
                font-size: 1.2em !important;
            }

            .tv-layout .digital-clock {
                font-size: 3em !important;
            }

            .tv-layout .prayer-times {
                height: 4cm !important;
                font-size: 1.3em !important;
            }

            .tv-layout .settings-btn {
                font-size: 2em !important;
            }

            /* أنماط الهاتف المحمول */
            .mobile-layout {
                font-size: 0.9em !important;
            }

            .mobile-layout .vertical-panel {
                width: 100% !important;
                height: auto !important;
                position: relative !important;
                order: 2;
            }

            .mobile-layout body {
                flex-direction: column !important;
            }

            .mobile-layout .prayer-times {
                width: 100% !important;
                position: relative !important;
                order: 3;
            }

            .mobile-layout .main-content {
                order: 1;
                width: 100% !important;
            }

            /* أنماط الجهاز اللوحي */
            .tablet-layout .vertical-panel {
                width: 6cm !important;
            }

            .tablet-layout .digital-clock {
                font-size: 2.5em !important;
            }

            /* أنماط الاتجاه العمودي */
            .portrait-orientation .vertical-panel {
                width: 100% !important;
                height: auto !important;
                position: relative !important;
                flex-direction: row !important;
                justify-content: space-around !important;
                padding: 10px !important;
            }

            .portrait-orientation body {
                flex-direction: column !important;
            }

            .portrait-orientation .prayer-times {
                width: 100% !important;
                position: relative !important;
                height: auto !important;
                flex-wrap: wrap !important;
            }

            .portrait-orientation .container {
                flex-direction: column !important;
                align-items: center !important;
            }

            /* أنماط الاتجاه الأفقي */
            .landscape-orientation body {
                flex-direction: row !important;
            }

            .landscape-orientation .vertical-panel {
                position: fixed !important;
                right: 0 !important;
                top: 0 !important;
                height: 100vh !important;
            }

            .landscape-orientation .prayer-times {
                position: fixed !important;
                bottom: 0 !important;
                left: 0 !important;
            }

            /* أنماط الشاشة الكاملة */
            .fullscreen-mode {
                overflow: hidden !important;
            }

            .fullscreen-mode .settings-btn {
                display: none !important;
            }

            /* أنماط PWA */
            .pwa-mode .day-display {
                top: 40px !important;
            }

            .pwa-mode .mosque-name-display {
                top: 40px !important;
            }

            /* تحسينات للمس */
            .touch-device .settings-btn {
                min-width: 44px !important;
                min-height: 44px !important;
            }

            .touch-device .font-size-btn {
                min-width: 44px !important;
                min-height: 44px !important;
            }

            .touch-device .zoom-controls button {
                min-width: 44px !important;
                min-height: 44px !important;
            }

            /* تحسينات للتلفاز */
            .tv-remote-navigation .settings-btn:focus,
            .tv-remote-navigation .font-size-btn:focus,
            .tv-remote-navigation button:focus {
                outline: 3px solid #D4AF37 !important;
                outline-offset: 2px !important;
            }
        `;
        document.head.appendChild(style);
    },

    // إعداد مستمعات الأحداث
    setupEventListeners: function() {
        // مستمع تغيير الاتجاه
        window.addEventListener('platformOrientationChange', (event) => {
            this.handleOrientationChange(event.detail.orientation);
        });

        // مستمع تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // مستمع الدخول/الخروج من الشاشة الكاملة
        document.addEventListener('fullscreenchange', () => {
            this.handleFullscreenChange();
        });

        // مستمع تغيير حالة PWA
        window.addEventListener('beforeinstallprompt', () => {
            this.handlePWAPrompt();
        });
    },

    // تطبيق التخطيط الأولي
    applyInitialLayout: function() {
        if (typeof PlatformDetector !== 'undefined') {
            const platform = PlatformDetector.getPlatformInfo();
            this.applyLayout(platform);
        }
    },

    // تطبيق التخطيط حسب المنصة
    applyLayout: function(platform) {
        const body = document.body;

        // إزالة الفئات السابقة
        this.removeLayoutClasses();

        // تطبيق فئة نوع الجهاز
        if (platform.isTV) {
            body.classList.add('tv-layout');
            this.currentLayout = 'tv';
            this.setupTVNavigation();
        } else if (platform.isMobile) {
            body.classList.add('mobile-layout');
            this.currentLayout = 'mobile';
            this.setupMobileLayout();
        } else if (platform.isTablet) {
            body.classList.add('tablet-layout');
            this.currentLayout = 'tablet';
            this.setupTabletLayout();
        } else {
            body.classList.add('desktop-layout');
            this.currentLayout = 'desktop';
        }

        // تطبيق فئة الاتجاه
        if (platform.orientation === 'portrait') {
            body.classList.add('portrait-orientation');
            this.currentOrientation = 'portrait';
        } else {
            body.classList.add('landscape-orientation');
            this.currentOrientation = 'landscape';
        }

        // تطبيق فئات إضافية
        if (platform.isPWA) {
            body.classList.add('pwa-mode');
        }

        if (this.isTouchDevice()) {
            body.classList.add('touch-device');
        }

        console.log(`تم تطبيق تخطيط: ${this.currentLayout} - ${this.currentOrientation}`);
    },

    // إزالة فئات التخطيط
    removeLayoutClasses: function() {
        const body = document.body;
        const layoutClasses = [
            'tv-layout', 'mobile-layout', 'tablet-layout', 'desktop-layout',
            'portrait-orientation', 'landscape-orientation',
            'pwa-mode', 'touch-device', 'fullscreen-mode', 'tv-remote-navigation'
        ];

        layoutClasses.forEach(className => {
            body.classList.remove(className);
        });
    },

    // إعداد تخطيط التلفاز
    setupTVNavigation: function() {
        document.body.classList.add('tv-remote-navigation');

        // تفعيل التنقل بالريموت
        this.enableRemoteNavigation();

        // تكبير الخطوط والعناصر
        this.adjustTVSizes();
    },

    // إعداد تخطيط الهاتف المحمول
    setupMobileLayout: function() {
        // إعادة ترتيب العناصر للهاتف المحمول
        this.rearrangeMobileElements();

        // تحسين اللمس
        this.optimizeForTouch();
    },

    // إعداد تخطيط الجهاز اللوحي
    setupTabletLayout: function() {
        // تحسينات خاصة بالجهاز اللوحي
        this.optimizeForTablet();
    },

    // تفعيل التنقل بالريموت للتلفاز
    enableRemoteNavigation: function() {
        const focusableElements = document.querySelectorAll(
            'button, [tabindex]:not([tabindex="-1"]), input, select'
        );

        focusableElements.forEach((element, index) => {
            element.setAttribute('tabindex', index + 1);
        });

        // التركيز على أول عنصر
        if (focusableElements.length > 0) {
            focusableElements[0].focus();
        }
    },

    // تعديل أحجام التلفاز
    adjustTVSizes: function() {
        const style = document.createElement('style');
        style.textContent = `
            .tv-layout * {
                font-size: 1.2em !important;
            }
            .tv-layout .prayer-name {
                font-size: 2.2em !important;
            }
            .tv-layout .prayer-hour {
                font-size: 2em !important;
            }
        `;
        document.head.appendChild(style);
    },

    // إعادة ترتيب عناصر الهاتف المحمول
    rearrangeMobileElements: function() {
        const body = document.body;
        const verticalPanel = document.querySelector('.vertical-panel');
        const prayerTimes = document.querySelector('.prayer-times');

        if (verticalPanel && prayerTimes) {
            // إنشاء حاوية رئيسية
            const mainContent = document.createElement('div');
            mainContent.className = 'main-content';

            // نقل المحتوى الرئيسي
            const children = Array.from(body.children);
            children.forEach(child => {
                if (child !== verticalPanel && child !== prayerTimes) {
                    mainContent.appendChild(child);
                }
            });

            body.appendChild(mainContent);
        }
    },

    // تحسين اللمس
    optimizeForTouch: function() {
        const touchElements = document.querySelectorAll('button, .clickable');
        touchElements.forEach(element => {
            element.style.minWidth = '44px';
            element.style.minHeight = '44px';
        });
    },

    // تحسين الجهاز اللوحي
    optimizeForTablet: function() {
        // تحسينات خاصة بالجهاز اللوحي
        const verticalPanel = document.querySelector('.vertical-panel');
        if (verticalPanel) {
            verticalPanel.style.width = '6cm';
        }
    },

    // التعامل مع تغيير الاتجاه
    handleOrientationChange: function(orientation) {
        console.log(`تغيير الاتجاه إلى: ${orientation}`);

        if (typeof PlatformDetector !== 'undefined') {
            const platform = PlatformDetector.getPlatformInfo();
            this.applyLayout(platform);
        }
    },

    // التعامل مع تغيير حجم النافذة
    handleResize: function() {
        if (typeof PlatformDetector !== 'undefined') {
            PlatformDetector.refresh();
            const platform = PlatformDetector.getPlatformInfo();
            this.applyLayout(platform);
        }
    },

    // التعامل مع تغيير الشاشة الكاملة
    handleFullscreenChange: function() {
        if (document.fullscreenElement) {
            document.body.classList.add('fullscreen-mode');
        } else {
            document.body.classList.remove('fullscreen-mode');
        }
    },

    // التعامل مع PWA
    handlePWAPrompt: function() {
        console.log('PWA متاح للتثبيت');
    },

    // التحقق من جهاز اللمس
    isTouchDevice: function() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    },

    // الحصول على التخطيط الحالي
    getCurrentLayout: function() {
        return {
            layout: this.currentLayout,
            orientation: this.currentOrientation
        };
    }
};

// تصدير الكائن للاستخدام العام
window.ResponsiveHandler = ResponsiveHandler;

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // انتظار تحميل PlatformDetector
    if (typeof PlatformDetector !== 'undefined') {
        PlatformDetector.initialize();
        ResponsiveHandler.initialize();
    } else {
        // إعادة المحاولة بعد فترة قصيرة
        setTimeout(() => {
            if (typeof PlatformDetector !== 'undefined') {
                PlatformDetector.initialize();
                ResponsiveHandler.initialize();
            }
        }, 100);
    }
});
