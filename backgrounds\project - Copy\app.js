function checkAdhanLibrary(maxRetries = 10, interval = 2000) {
    let retries = 0;
    
    function tryInitialize() {
        if (window.initAdhan()) {
            console.log('تم العثور على مكتبة Adhan');
            initializePrayerTimes();
            return;
        }
        
        if (retries < maxRetries) {
            retries++;
            console.log(`محاولة ${retries} من ${maxRetries} للتحقق من مكتبة Adhan`);
            setTimeout(tryInitialize, interval);
        } else {
            console.error('تعذر تحميل مكتبة Adhan - استخدام الأوقات الافتراضية');
            useFallbackPrayerTimes();
        }
    }
    
    tryInitialize();
}

document.addEventListener('DOMContentLoaded', function() {
    checkAdhanLibrary();
});

// تحسين دالة تهيئة أوقات الصلاة
function initializePrayerTimes() {
    try {
        if (!window.Adhan) {
            throw new Error('مكتبة Adhan غير متوفرة');
        }

        // تهيئة المدير مع التحقق من وجوده
        if (!window.prayerManager) {
            window.prayerManager = new PrayerTimesManager();
        }

        // التأكد من تهيئة المدير بشكل صحيح
        if (!prayerManager.calculatePrayerTimes) {
            throw new Error('خطأ في تهيئة مدير الصلوات');
        }

        // إعداد المدينة الافتراضية
        prayerManager.setCity(21.3891, 39.8579);
        
        // التحديث الأولي
        const times = prayerManager.calculatePrayerTimes();
        if (times) {
            window.currentPrayerTimes = times; // تخزين عالمي
            updatePrayerTimesDisplay(times);
            displayRemainingPrayerTimes();
        }

        // تحديث دوري
        setInterval(() => {
            const times = prayerManager.calculatePrayerTimes();
            if (times) {
                window.currentPrayerTimes = times;
                updatePrayerTimesDisplay(times);
            }
        }, 60000);

        return true;
    } catch (error) {
        console.error('خطأ في التهيئة:', error);
        useFallbackPrayerTimes();
        return false;
    }
}

function updatePrayerTimesDisplay(times) {
    if (!times) return;
    
    const elements = {
        'fajr-time': times.fajr,
        'sunrise-time': times.sunrise,
        'dhuhr-time': times.dhuhr,
        'asr-time': times.asr,
        'maghrib-time': times.maghrib,
        'isha-time': times.isha
    };

    for (const [id, time] of Object.entries(elements)) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = time;
        }
    }
}

function useFallbackPrayerTimes() {
    const fallbackTimes = {
        fajr: "04:00",
        sunrise: "05:30",
        dhuhr: "12:00",
        asr: "15:30",
        maghrib: "18:30",
        isha: "20:00"
    };
    updatePrayerTimesDisplay(fallbackTimes);
}

function updateNextPrayer() {
    const nextPrayer = prayerManager.getNextPrayer();
    if (nextPrayer) {
        const element = document.getElementById('next-prayer');
        if (element) {
            element.textContent = `الصلاة القادمة: ${nextPrayer.name} - ${nextPrayer.time}`;
        }
    }
}
