// دالة لبدء العد التنازلي للإقامة في منتصف الشاشة
function startFullScreenIqamaCountdown(prayerName, arabicName) {
    console.log(`بدء العد التنازلي للإقامة لصلاة ${arabicName} (${prayerName})...`);
    
    // إلغاء أي مؤقت سابق للإقامة
    if (window.iqamaCountdownTimer) {
        clearInterval(window.iqamaCountdownTimer);
        window.iqamaCountdownTimer = null;
    }
    
    // الحصول على مدة الإقامة بالدقائق
    let iqamaMinutes = 10; // القيمة الافتراضية
    
    try {
        // محاولة الحصول على مدة الإقامة من التخزين المحلي
        const savedDurations = JSON.parse(localStorage.getItem('iqama_durations')) || {};
        if (savedDurations && savedDurations[prayerName]) {
            iqamaMinutes = savedDurations[prayerName];
            console.log(`تم استخدام مدة الإقامة المحفوظة: ${iqamaMinutes} دقيقة`);
        } else {
            // محاولة الحصول على مدة الإقامة من حقول الإدخال
            const inputElement = document.getElementById(`${prayerName}-iqama-duration`);
            if (inputElement) {
                iqamaMinutes = parseInt(inputElement.value) || 10;
                console.log(`تم استخدام مدة الإقامة من حقل الإدخال: ${iqamaMinutes} دقيقة`);
            } else {
                console.log(`استخدام القيمة الافتراضية لمدة الإقامة: ${iqamaMinutes} دقيقة`);
            }
        }
    } catch (error) {
        console.error(`خطأ في الحصول على مدة الإقامة: ${error.message}`);
    }
    
    // تحويل إلى ثواني
    let secondsLeft = iqamaMinutes * 60;
    
    // إنشاء عنصر التعتيم إذا لم يكن موجودًا
    let darknessOverlay = document.getElementById('darkness-overlay');
    if (!darknessOverlay) {
        darknessOverlay = document.createElement('div');
        darknessOverlay.id = 'darkness-overlay';
        darknessOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: black;
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 1s ease;
        `;
        document.body.appendChild(darknessOverlay);
    } else {
        // إزالة أي محتوى سابق
        while (darknessOverlay.firstChild) {
            darknessOverlay.removeChild(darknessOverlay.firstChild);
        }
    }
    
    // إنشاء عنصر العد التنازلي
    const countdownDisplay = document.createElement('div');
    countdownDisplay.id = 'fullscreen-countdown';
    countdownDisplay.style.cssText = `
        color: white;
        font-size: 10vw;
        font-weight: bold;
        text-align: center;
        direction: rtl;
        font-family: 'Amiri', 'Traditional Arabic', Arial, sans-serif;
    `;
    darknessOverlay.appendChild(countdownDisplay);
    
    // إضافة زر إغلاق
    const closeButton = document.createElement('button');
    closeButton.style.cssText = `
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        font-size: 24px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
    `;
    closeButton.innerHTML = '×';
    closeButton.onclick = function() {
        // إلغاء العد التنازلي
        if (window.iqamaCountdownTimer) {
            clearInterval(window.iqamaCountdownTimer);
            window.iqamaCountdownTimer = null;
        }
        
        // إخفاء التعتيم
        darknessOverlay.style.opacity = '0';
        setTimeout(() => {
            darknessOverlay.style.display = 'none';
            
            // إعادة تشغيل النص المتغير
            if (typeof startTextChange === 'function') {
                startTextChange();
            }
        }, 1000);
    };
    darknessOverlay.appendChild(closeButton);
    
    // إظهار التعتيم
    darknessOverlay.style.display = 'flex';
    darknessOverlay.style.opacity = '1';
    
    // إيقاف النص المتغير
    if (window.textChangeInterval) {
        clearInterval(window.textChangeInterval);
        window.textChangeInterval = null;
        console.log('تم إيقاف النص المتغير');
    }
    
    // تحديث العد التنازلي
    function updateCountdown() {
        const minutes = Math.floor(secondsLeft / 60);
        const seconds = secondsLeft % 60;
        
        countdownDisplay.innerHTML = `
            <div style="margin-bottom: 20px; font-size: 5vw;">صلاة ${arabicName}</div>
            <div style="font-size: 15vw; margin-bottom: 20px;">
                ${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}
            </div>
            <div style="font-size: 4vw;">الوقت المتبقي للإقامة</div>
        `;
        
        if (secondsLeft <= 0) {
            clearInterval(window.iqamaCountdownTimer);
            window.iqamaCountdownTimer = null;
            
            // تحديث النص عند انتهاء العد التنازلي
            countdownDisplay.innerHTML = `
                <div style="margin-bottom: 20px; font-size: 5vw;">صلاة ${arabicName}</div>
                <div style="font-size: 15vw; margin-bottom: 20px;">00:00</div>
                <div style="font-size: 4vw;">حان وقت الإقامة</div>
            `;
            
            // تشغيل صوت الإقامة
            const iqamaAudio = document.getElementById('iqama-audio');
            if (iqamaAudio) {
                iqamaAudio.play()
                    .then(() => {
                        console.log('بدأ تشغيل صوت الإقامة');
                        
                        // عرض الساعة الرقمية بعد ثانيتين
                        setTimeout(() => {
                            showDigitalClock(darknessOverlay);
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('خطأ في تشغيل صوت الإقامة:', err);
                        
                        // عرض الساعة الرقمية في حالة الخطأ
                        showDigitalClock(darknessOverlay);
                    });
            } else {
                console.error('عنصر صوت الإقامة غير موجود');
                
                // عرض الساعة الرقمية في حالة عدم وجود صوت الإقامة
                showDigitalClock(darknessOverlay);
            }
        }
        
        secondsLeft--;
    }
    
    // تحديث العد التنازلي مباشرة ثم كل ثانية
    updateCountdown();
    window.iqamaCountdownTimer = setInterval(updateCountdown, 1000);
}

// دالة لعرض الساعة الرقمية على كامل الشاشة
function showDigitalClock(darknessOverlay) {
    console.log('عرض الساعة الرقمية على كامل الشاشة');
    
    // إزالة أي محتوى سابق
    while (darknessOverlay.firstChild) {
        darknessOverlay.removeChild(darknessOverlay.firstChild);
    }
    
    // إنشاء عنصر الساعة الرقمية
    const digitalClock = document.createElement('div');
    digitalClock.id = 'fullscreen-digital-clock';
    digitalClock.style.cssText = `
        color: white;
        font-size: 20vw;
        font-weight: bold;
        text-align: center;
        direction: ltr;
        font-family: 'Digital-7', 'Courier New', monospace;
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.7);
    `;
    darknessOverlay.appendChild(digitalClock);
    
    // إضافة زر إغلاق
    const closeButton = document.createElement('button');
    closeButton.style.cssText = `
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        font-size: 24px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
    `;
    closeButton.innerHTML = '×';
    closeButton.onclick = function() {
        // إلغاء تحديث الساعة
        if (window.digitalClockInterval) {
            clearInterval(window.digitalClockInterval);
            window.digitalClockInterval = null;
        }
        
        // إخفاء التعتيم
        darknessOverlay.style.opacity = '0';
        setTimeout(() => {
            darknessOverlay.style.display = 'none';
            
            // إعادة تشغيل النص المتغير
            if (typeof startTextChange === 'function') {
                startTextChange();
            }
        }, 1000);
    };
    darknessOverlay.appendChild(closeButton);
    
    // تحديث الساعة الرقمية
    function updateDigitalClock() {
        const now = new Date();
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');
        digitalClock.textContent = `${hours}:${minutes}:${seconds}`;
    }
    
    // تحديث الساعة مباشرة ثم كل ثانية
    updateDigitalClock();
    
    // إلغاء أي مؤقت سابق للساعة الرقمية
    if (window.digitalClockInterval) {
        clearInterval(window.digitalClockInterval);
    }
    
    // بدء تحديث الساعة كل ثانية
    window.digitalClockInterval = setInterval(updateDigitalClock, 1000);
    
    // الحصول على مدة التعتيم المحفوظة (10 دقائق افتراضيًا)
    const darknessMinutes = 10;
    
    // إخفاء التعتيم بعد المدة المحددة
    setTimeout(() => {
        // إلغاء تحديث الساعة
        if (window.digitalClockInterval) {
            clearInterval(window.digitalClockInterval);
            window.digitalClockInterval = null;
        }
        
        // إخفاء التعتيم
        darknessOverlay.style.opacity = '0';
        setTimeout(() => {
            darknessOverlay.style.display = 'none';
            
            // إعادة تشغيل النص المتغير
            if (typeof startTextChange === 'function') {
                startTextChange();
            }
        }, 1000);
    }, darknessMinutes * 60 * 1000);
}

// دالة لاختبار العد التنازلي للإقامة
function testIqamaCountdown() {
    console.log('اختبار العد التنازلي للإقامة...');
    
    // بدء العد التنازلي للإقامة
    startFullScreenIqamaCountdown('asr', 'العصر');
}
