# إعداد تطبيق سطح المكتب لساعة المسجد

## الطرق المتاحة لإنشاء تطبيق سطح المكتب

### 1. Electron (الأكثر شيوعاً)

#### تثبيت Electron:

```bash
# إنشاء مجلد المشروع
mkdir mosque-clock-desktop
cd mosque-clock-desktop

# تهيئة npm
npm init -y

# تثبيت Electron
npm install electron --save-dev
npm install electron-builder --save-dev
npm install electron-updater --save
```

#### ملف main.js (نقطة البداية):

```javascript
const { app, BrowserWindow, Menu, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const isDev = require('electron-is-dev');
const { autoUpdater } = require('electron-updater');

// متغيرات عامة
let mainWindow;
let splashWindow;

// إنشاء النافذة الرئيسية
function createMainWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        show: false,
        icon: path.join(__dirname, 'assets/icon.png'),
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'preload.js')
        },
        titleBarStyle: 'default',
        frame: true,
        resizable: true,
        maximizable: true,
        fullscreenable: true
    });

    // تحميل التطبيق
    const startUrl = isDev 
        ? 'http://localhost:3000' 
        : `file://${path.join(__dirname, '../build/index.html')}`;
    
    mainWindow.loadURL(startUrl);

    // إظهار النافذة عند الانتهاء من التحميل
    mainWindow.once('ready-to-show', () => {
        if (splashWindow) {
            splashWindow.close();
        }
        mainWindow.show();
        
        // التحقق من التحديثات
        if (!isDev) {
            autoUpdater.checkForUpdatesAndNotify();
        }
    });

    // التعامل مع إغلاق النافذة
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // فتح الروابط في المتصفح الخارجي
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });
}

// إنشاء شاشة البداية
function createSplashWindow() {
    splashWindow = new BrowserWindow({
        width: 400,
        height: 300,
        frame: false,
        alwaysOnTop: true,
        transparent: true,
        webPreferences: {
            nodeIntegration: false
        }
    });

    splashWindow.loadFile('splash.html');
    
    splashWindow.on('closed', () => {
        splashWindow = null;
    });
}

// تهيئة التطبيق
app.whenReady().then(() => {
    createSplashWindow();
    
    setTimeout(() => {
        createMainWindow();
        createMenu();
    }, 2000);

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createMainWindow();
        }
    });
});

// إنهاء التطبيق عند إغلاق جميع النوافذ
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// إنشاء القائمة
function createMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'إعدادات',
                    accelerator: 'CmdOrCtrl+,',
                    click: () => {
                        mainWindow.webContents.send('open-settings');
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                {
                    label: 'إعادة تحميل',
                    accelerator: 'CmdOrCtrl+R',
                    click: () => {
                        mainWindow.reload();
                    }
                },
                {
                    label: 'شاشة كاملة',
                    accelerator: 'F11',
                    click: () => {
                        mainWindow.setFullScreen(!mainWindow.isFullScreen());
                    }
                },
                { type: 'separator' },
                {
                    label: 'تكبير',
                    accelerator: 'CmdOrCtrl+Plus',
                    click: () => {
                        const currentZoom = mainWindow.webContents.getZoomLevel();
                        mainWindow.webContents.setZoomLevel(currentZoom + 0.5);
                    }
                },
                {
                    label: 'تصغير',
                    accelerator: 'CmdOrCtrl+-',
                    click: () => {
                        const currentZoom = mainWindow.webContents.getZoomLevel();
                        mainWindow.webContents.setZoomLevel(currentZoom - 0.5);
                    }
                },
                {
                    label: 'حجم طبيعي',
                    accelerator: 'CmdOrCtrl+0',
                    click: () => {
                        mainWindow.webContents.setZoomLevel(0);
                    }
                }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول التطبيق',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'حول ساعة المسجد',
                            message: 'ساعة المسجد v2.0.0',
                            detail: 'تطبيق شامل لعرض مواقيت الصلاة والساعة للمساجد'
                        });
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// التعامل مع التحديثات
autoUpdater.on('checking-for-update', () => {
    console.log('البحث عن تحديثات...');
});

autoUpdater.on('update-available', (info) => {
    console.log('تحديث متاح');
});

autoUpdater.on('update-not-available', (info) => {
    console.log('لا توجد تحديثات');
});

autoUpdater.on('error', (err) => {
    console.log('خطأ في التحديث: ', err);
});

autoUpdater.on('download-progress', (progressObj) => {
    let log_message = "سرعة التحميل: " + progressObj.bytesPerSecond;
    log_message = log_message + ' - تم تحميل ' + progressObj.percent + '%';
    log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')';
    console.log(log_message);
});

autoUpdater.on('update-downloaded', (info) => {
    console.log('تم تحميل التحديث');
    autoUpdater.quitAndInstall();
});
```

#### ملف package.json:

```json
{
  "name": "mosque-clock-desktop",
  "version": "2.0.0",
  "description": "تطبيق ساعة المسجد لسطح المكتب",
  "main": "main.js",
  "homepage": "./",
  "scripts": {
    "start": "electron .",
    "dev": "electron . --dev",
    "build": "electron-builder",
    "build-win": "electron-builder --win",
    "build-mac": "electron-builder --mac",
    "build-linux": "electron-builder --linux",
    "dist": "npm run build && electron-builder --publish=never",
    "pack": "electron-builder --dir",
    "postinstall": "electron-builder install-app-deps"
  },
  "build": {
    "appId": "com.mosque.clock.desktop",
    "productName": "ساعة المسجد",
    "directories": {
      "output": "dist"
    },
    "files": [
      "build/**/*",
      "node_modules/**/*",
      "main.js",
      "preload.js",
      "splash.html"
    ],
    "win": {
      "target": "nsis",
      "icon": "assets/icon.ico",
      "requestedExecutionLevel": "asInvoker"
    },
    "mac": {
      "target": "dmg",
      "icon": "assets/icon.icns",
      "category": "public.app-category.productivity"
    },
    "linux": {
      "target": "AppImage",
      "icon": "assets/icon.png",
      "category": "Office"
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true,
      "shortcutName": "ساعة المسجد"
    },
    "publish": {
      "provider": "github",
      "owner": "mosque-clock",
      "repo": "desktop-app"
    }
  },
  "devDependencies": {
    "electron": "^22.0.0",
    "electron-builder": "^23.6.0"
  },
  "dependencies": {
    "electron-is-dev": "^2.0.0",
    "electron-updater": "^5.3.0"
  }
}
```

### 2. Tauri (أسرع وأخف)

#### تثبيت Tauri:

```bash
# تثبيت Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# إنشاء مشروع Tauri
npm create tauri-app@latest mosque-clock-tauri
cd mosque-clock-tauri

# تثبيت التبعيات
npm install
```

#### ملف tauri.conf.json:

```json
{
  "build": {
    "beforeDevCommand": "npm run dev",
    "beforeBuildCommand": "npm run build",
    "devPath": "http://localhost:1420",
    "distDir": "../dist"
  },
  "package": {
    "productName": "ساعة المسجد",
    "version": "2.0.0"
  },
  "tauri": {
    "allowlist": {
      "all": false,
      "shell": {
        "all": false,
        "open": true
      },
      "notification": {
        "all": true
      },
      "window": {
        "all": false,
        "close": true,
        "hide": true,
        "show": true,
        "maximize": true,
        "minimize": true,
        "unmaximize": true,
        "unminimize": true,
        "startDragging": true
      }
    },
    "bundle": {
      "active": true,
      "targets": "all",
      "identifier": "com.mosque.clock.tauri",
      "icon": [
        "icons/32x32.png",
        "icons/128x128.png",
        "icons/<EMAIL>",
        "icons/icon.icns",
        "icons/icon.ico"
      ]
    },
    "security": {
      "csp": null
    },
    "windows": [
      {
        "fullscreen": false,
        "resizable": true,
        "title": "ساعة المسجد",
        "width": 1200,
        "height": 800,
        "minWidth": 800,
        "minHeight": 600
      }
    ]
  }
}
```

### 3. PWA Desktop (Chrome/Edge)

#### تثبيت كتطبيق سطح مكتب:
1. افتح الموقع في Chrome أو Edge
2. انقر على أيقونة التثبيت في شريط العنوان
3. أو من القائمة: "تثبيت ساعة المسجد"

#### المميزات:
- ✅ سهولة التثبيت
- ✅ تحديثات تلقائية
- ✅ استهلاك ذاكرة أقل
- ✅ تكامل مع نظام التشغيل

## إعدادات خاصة بالتلفاز

### للتحكم بالريموت:

```javascript
// في main.js
mainWindow = new BrowserWindow({
    // ... إعدادات أخرى
    kiosk: true, // وضع الكشك للتلفاز
    autoHideMenuBar: true,
    webPreferences: {
        // ... إعدادات أخرى
        additionalArguments: ['--enable-features=OverlayScrollbar']
    }
});

// تفعيل التنقل بالمفاتيح
mainWindow.webContents.on('before-input-event', (event, input) => {
    // التعامل مع مفاتيح الريموت
    if (input.key === 'ArrowUp' || input.key === 'ArrowDown' || 
        input.key === 'ArrowLeft' || input.key === 'ArrowRight' ||
        input.key === 'Enter' || input.key === 'Escape') {
        // إرسال الحدث للتطبيق
        mainWindow.webContents.send('remote-key', input.key);
    }
});
```

## البناء والتوزيع

### Windows:

```bash
# بناء للويندوز
npm run build-win

# إنشاء مثبت
electron-builder --win --publish=never
```

### macOS:

```bash
# بناء للماك
npm run build-mac

# إنشاء DMG
electron-builder --mac --publish=never
```

### Linux:

```bash
# بناء للينكس
npm run build-linux

# إنشاء AppImage
electron-builder --linux --publish=never
```

## التوقيع والأمان

### Windows Code Signing:

```bash
# شراء شهادة من CA معتمد
# تكوين electron-builder
"win": {
  "certificateFile": "path/to/certificate.p12",
  "certificatePassword": "password",
  "signingHashAlgorithms": ["sha256"],
  "signDlls": true
}
```

### macOS Code Signing:

```bash
# تسجيل في Apple Developer Program
# تكوين electron-builder
"mac": {
  "hardenedRuntime": true,
  "entitlements": "entitlements.mac.plist",
  "entitlementsInherit": "entitlements.mac.plist",
  "gatekeeperAssess": false
}
```

## الاختبار

### اختبار على منصات مختلفة:
1. Windows 10/11
2. macOS (Intel & Apple Silicon)
3. Linux (Ubuntu, Fedora, etc.)
4. مختلف أحجام الشاشات
5. اختبار الأداء

### أدوات الاختبار:
- Virtual machines
- GitHub Actions CI/CD
- Local testing on multiple devices

## النشر

### Microsoft Store:
1. تسجيل حساب مطور
2. إنشاء حزمة MSIX
3. رفع التطبيق
4. مراجعة ونشر

### Mac App Store:
1. Apple Developer Account
2. App Store Connect
3. تحضير التطبيق
4. مراجعة ونشر

### Linux:
- Snap Store
- Flatpak
- AppImage
- توزيع مباشر
