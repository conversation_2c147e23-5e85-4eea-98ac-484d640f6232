#include <Wire.h>
#include <RTClib.h>
#include <EEPROM.h>
#include <math.h>
#include <LiquidCrystal_I2C.h>

// تعريف كائنات الأجهزة
RTC_DS3231 rtc;
LiquidCrystal_I2C lcd(0x27, 16, 2);

// هيكل مواقيت الصلاة
struct PrayerTimes {
  String fajr;
  String sunrise;
  String dhuhr;
  String asr;
  String maghrib;
  String isha;
};

// هيكل إعدادات الموقع
struct LocationSettings {
  float latitude;
  float longitude;
  int timeZone;
  byte calcMethod;
  byte asrMethod;
  byte adjustments[6];
  char cityName[30];
};

// ثوابت طرق الحساب
enum CalculationMethod {
  MWL, ISNA, Egypt, Makkah, Karachi, Custom
};

// ثوابت طريقة العصر
enum AsrJuristic {
  Sha<PERSON>i, Han<PERSON>i
};

LocationSettings location;
PrayerTimes currentTimes;

// قاعدة بيانات المدن (مثال مختصر)
const struct {
  char name[30];
  float lat;
  float lng;
  int tz;
} cities[] = {
  {"مكة المكرمة", 21.3891, 39.8579, 3},
  {"المدينة المنورة", 24.5247, 39.5692, 3},
  {"الرياض", 24.7136, 46.6753, 3},
  {"القاهرة", 30.0444, 31.2357, 2},
  // يمكن إضافة جميع المدن المطلوبة هنا
};

void setup() {
  Serial.begin(9600);
  
  // تهيئة الأجهزة
  if (!rtc.begin()) {
    Serial.println("خطأ في الساعة");
    while (1);
  }
  
  lcd.init();
  lcd.backlight();
  
  // تحميل الإعدادات
  loadLocationSettings();
  
  // حساب المواقيت
  calculatePrayerTimes();
  
  // العرض الأولي
  displayTimes();
}

void loop() {
  static unsigned long lastUpdate = 0;
  if (millis() - lastUpdate >= 60000) {
    lastUpdate = millis();
    DateTime now = rtc.now();
    
    if (now.hour() == 0 && now.minute() == 0) {
      calculatePrayerTimes();
    }
    
    checkPrayerTimes();
    displayTimes();
  }
  
  // هنا يتم إضافة كود الأزرار للتحكم
}

// الخوارزميات الدقيقة لحساب المواقيت
void calculatePrayerTimes() {
  DateTime now = rtc.now();
  int year = now.year();
  int month = now.month();
  int day = now.day();
  
  // حساب الزوايا بناء على طريقة الحساب
  double fajrAngle, ishaAngle;
  switch(location.calcMethod) {
    case MWL: fajrAngle = 18; ishaAngle = 17; break;
    case ISNA: fajrAngle = 15; ishaAngle = 15; break;
    case Egypt: fajrAngle = 19.5; ishaAngle = 17.5; break;
    case Makkah: fajrAngle = 18.5; ishaAngle = 90; break; // خاصة بالعشاء
    case Karachi: fajrAngle = 18; ishaAngle = 18; break;
    default: fajrAngle = 18; ishaAngle = 17;
  }
  
  // الحسابات الفلكية
  double julianDate = calculateJulianDate(year, month, day) - location.longitude / (15 * 24);
  double sunPosition = calculateSunPosition(julianDate);
  
  // إضافة معادلة الوقت (Equation of Time) وتصحيح انكسار الضوء الجوي
  double eqTime = calculateEquationOfTime(julianDate);
  double declination = calculateSunDeclination(julianDate);
  
  // حساب الأوقات
  currentTimes.fajr = computeTimeWithCorrections(180 - fajrAngle, declination, eqTime, false);
  currentTimes.sunrise = computeTimeWithCorrections(180 - 0.833, declination, eqTime, false);
  currentTimes.dhuhr = computeDhuhrTime(eqTime);
  currentTimes.asr = computeAsrTime(declination, location.asrMethod == Hanafi ? 1 : 0, eqTime);
  currentTimes.maghrib = computeTimeWithCorrections(0.833, declination, eqTime, true);
  currentTimes.isha = computeTimeWithCorrections(ishaAngle, declination, eqTime, true);
  
  applyManualAdjustments();
}

// حساب التاريخ اليولياني
double calculateJulianDate(int year, int month, int day) {
  if (month <= 2) {
    year -= 1;
    month += 12;
  }
  int A = floor(year / 100);
  int B = 2 - A + floor(A / 4);
  return floor(365.25 * (year + 4716)) + floor(30.6001 * (month + 1)) + day + B - 1524.5;
}

// حساب موقع الشمس (زاوية الشمس)
double calculateSunPosition(double julianDate) {
  double D = julianDate - 2451545.0;
  double g = 357.529 + 0.98560028 * D;
  double q = 280.459 + 0.98564736 * D;
  double L = q + 1.915 * sin(g * M_PI / 180) + 0.020 * sin(2 * g * M_PI / 180);
  
  L = fmod(L, 360);
  if (L < 0) L += 360;
  
  return L;
}

// حساب معادلة الوقت (Equation of Time)
double calculateEquationOfTime(double julianDate) {
  double D = julianDate - 2451545.0;
  double g = 357.529 + 0.98560028 * D;
  double q = 280.459 + 0.98564736 * D;
  double L = q + 1.915 * sin(g * M_PI / 180) + 0.020 * sin(2 * g * M_PI / 180);
  
  double e = 23.439 - 0.00000036 * D;
  
  double RA = atan2(cos(e * M_PI / 180) * sin(L * M_PI / 180), cos(L * M_PI / 180)) * 180 / M_PI;
  RA = fmod(RA, 360);
  if (RA < 0) RA += 360;
  
  double eqTime = q / 15 - RA / 15;
  return eqTime * 60; // بالدقائق
}

// حساب ميل الشمس (Declination)
double calculateSunDeclination(double julianDate) {
  double D = julianDate - 2451545.0;
  double g = 357.529 + 0.98560028 * D;
  double q = 280.459 + 0.98564736 * D;
  double L = q + 1.915 * sin(g * M_PI / 180) + 0.020 * sin(2 * g * M_PI / 180);
  
  double e = 23.439 - 0.00000036 * D;
  
  double decl = asin(sin(e * M_PI / 180) * sin(L * M_PI / 180)) * 180 / M_PI;
  return decl;
}

// حساب وقت الظهر مع تصحيح معادلة الوقت
String computeDhuhrTime(double eqTime) {
  double time = 12 + location.timeZone - (location.longitude / 15) - (eqTime / 60);
  return formatTime(time);
}

// حساب وقت العصر مع التصحيحات
String computeAsrTime(double declination, int method, double eqTime) {
  double latRad = location.latitude * M_PI / 180;
  double declRad = declination * M_PI / 180;
  
  double angle = -atan(1.0 / (method + tan(abs(latRad - declRad))));
  double hourAngle = acos((sin(angle) - sin(latRad) * sin(declRad)) / (cos(latRad) * cos(declRad)));
  
  double dhuhrTime = 12 + location.timeZone - (location.longitude / 15) - (eqTime / 60);
  double asrTime = dhuhrTime + hourAngle * 180 / M_PI / 15;
  
  return formatTime(asrTime);
}

// حساب الأوقات مع التصحيحات
String computeTimeWithCorrections(double angle, double declination, double eqTime, bool isAfterNoon) {
  double latRad = location.latitude * M_PI / 180;
  double declRad = declination * M_PI / 180;
  double angleRad = angle * M_PI / 180;
  
  double hourAngle = acos((sin(angleRad) - sin(latRad) * sin(declRad)) / (cos(latRad) * cos(declRad)));
  
  double dhuhrTime = 12 + location.timeZone - (location.longitude / 15) - (eqTime / 60);
  double time = isAfterNoon ? dhuhrTime + hourAngle * 180 / M_PI / 15 : dhuhrTime - hourAngle * 180 / M_PI / 15;
  
  return formatTime(time);
}

// تنسيق الوقت
String formatTime(double time) {
  time = fmod(time + 24, 24);
  int hours = floor(time);
  int minutes = floor((time - hours) * 60);
  
  hours %= 24;
  if (hours < 0) hours += 24;
  
  return (hours < 10 ? "0" : "") + String(hours) + ":" + 
         (minutes < 10 ? "0" : "") + String(minutes);
}

// تطبيق التعديلات اليدوية
void applyManualAdjustments() {
  // مثال على تطبيق تعديلات يدوية على أوقات الصلاة
  // التعديلات مخزنة في location.adjustments: [fajr, sunrise, dhuhr, asr, maghrib, isha] بالدقائق
  currentTimes.fajr = adjustTime(currentTimes.fajr, location.adjustments[0]);
  currentTimes.sunrise = adjustTime(currentTimes.sunrise, location.adjustments[1]);
  currentTimes.dhuhr = adjustTime(currentTimes.dhuhr, location.adjustments[2]);
  currentTimes.asr = adjustTime(currentTimes.asr, location.adjustments[3]);
  currentTimes.maghrib = adjustTime(currentTimes.maghrib, location.adjustments[4]);
  currentTimes.isha = adjustTime(currentTimes.isha, location.adjustments[5]);
}

// دالة مساعدة لتعديل الوقت
String adjustTime(String timeStr, int adjustment) {
  int hour = timeStr.substring(0, 2).toInt();
  int minute = timeStr.substring(3, 5).toInt();
  
  minute += adjustment;
  if (minute >= 60) {
    hour += minute / 60;
    minute = minute % 60;
  } else if (minute < 0) {
    hour -= 1 + (-minute) / 60;
    minute = 60 - ((-minute) % 60);
  }
  
  hour = (hour + 24) % 24;
  
  return (hour < 10 ? "0" : "") + String(hour) + ":" + (minute < 10 ? "0" : "") + String(minute);
}

// دوال تحميل الإعدادات، العرض، والتنبيه تبقى كما هي
// ...
