/**
 * ملف مواقيت الصلاة الجديد
 * يحتوي على جميع الدوال المتعلقة بمواقيت الصلاة
 */

// تعريف مواقيت الصلاة الثابتة لجميع المدن
const PRAYER_TIMES_DATA = {
    // طريقة رابطة العالم الإسلامي (MWL)
    'MWL': {
        'Asia/Amman': {
            fajr: '04:15',
            sunrise: '05:45',
            dhuhr: '12:30',
            asr: '16:00',
            maghrib: '19:15',
            isha: '20:45'
        },
        'Asia/Riyadh': {
            fajr: '04:00',
            sunrise: '05:30',
            dhuhr: '12:15',
            asr: '15:45',
            maghrib: '19:00',
            isha: '20:30'
        },
        'Asia/Dubai': {
            fajr: '04:30',
            sunrise: '06:00',
            dhuhr: '12:45',
            asr: '16:15',
            maghrib: '19:30',
            isha: '21:00'
        },
        'Africa/Cairo': {
            fajr: '04:10',
            sunrise: '05:40',
            dhuhr: '12:25',
            asr: '15:55',
            maghrib: '19:10',
            isha: '20:40'
        },
        'Asia/Jerusalem': {
            fajr: '04:20',
            sunrise: '05:50',
            dhuhr: '12:35',
            asr: '16:05',
            maghrib: '19:20',
            isha: '20:50'
        },
        'Asia/Makkah': {
            fajr: '04:40',
            sunrise: '06:10',
            dhuhr: '12:20',
            asr: '15:50',
            maghrib: '18:30',
            isha: '20:00'
        },
        'Asia/Madinah': {
            fajr: '04:35',
            sunrise: '06:05',
            dhuhr: '12:15',
            asr: '15:45',
            maghrib: '18:25',
            isha: '19:55'
        },
        'Asia/Istanbul': {
            fajr: '04:05',
            sunrise: '05:35',
            dhuhr: '13:00',
            asr: '16:30',
            maghrib: '20:25',
            isha: '21:55'
        },
        'Asia/Baghdad': {
            fajr: '04:05',
            sunrise: '05:35',
            dhuhr: '12:20',
            asr: '15:50',
            maghrib: '19:05',
            isha: '20:35'
        },
        'Asia/Kuwait': {
            fajr: '03:55',
            sunrise: '05:25',
            dhuhr: '11:55',
            asr: '15:25',
            maghrib: '18:25',
            isha: '19:55'
        },
        'Asia/Doha': {
            fajr: '03:50',
            sunrise: '05:20',
            dhuhr: '11:50',
            asr: '15:20',
            maghrib: '18:20',
            isha: '19:50'
        },
        'Asia/Muscat': {
            fajr: '04:25',
            sunrise: '05:55',
            dhuhr: '12:25',
            asr: '15:55',
            maghrib: '18:55',
            isha: '20:25'
        },
        'Asia/Beirut': {
            fajr: '04:15',
            sunrise: '05:45',
            dhuhr: '12:30',
            asr: '16:00',
            maghrib: '19:15',
            isha: '20:45'
        },
        'Africa/Tunis': {
            fajr: '04:05',
            sunrise: '05:35',
            dhuhr: '12:45',
            asr: '16:15',
            maghrib: '19:55',
            isha: '21:25'
        },
        'Africa/Algiers': {
            fajr: '04:10',
            sunrise: '05:40',
            dhuhr: '12:50',
            asr: '16:20',
            maghrib: '20:00',
            isha: '21:30'
        },
        'Africa/Casablanca': {
            fajr: '04:30',
            sunrise: '06:00',
            dhuhr: '13:10',
            asr: '16:40',
            maghrib: '20:20',
            isha: '21:50'
        },
        'Asia/Tehran': {
            fajr: '04:35',
            sunrise: '06:05',
            dhuhr: '13:05',
            asr: '16:35',
            maghrib: '20:05',
            isha: '21:35'
        },
        'Asia/Karachi': {
            fajr: '04:45',
            sunrise: '06:15',
            dhuhr: '12:15',
            asr: '15:45',
            maghrib: '18:15',
            isha: '19:45'
        },
        'Asia/Kuala_Lumpur': {
            fajr: '05:50',
            sunrise: '07:20',
            dhuhr: '13:20',
            asr: '16:50',
            maghrib: '19:20',
            isha: '20:50'
        },
        'Asia/Jakarta': {
            fajr: '04:40',
            sunrise: '06:10',
            dhuhr: '12:10',
            asr: '15:40',
            maghrib: '18:10',
            isha: '19:40'
        }
    },
    
    // طريقة الجمعية الإسلامية لأمريكا الشمالية (ISNA)
    'ISNA': {
        'Asia/Amman': {
            fajr: '04:30',
            sunrise: '05:45',
            dhuhr: '12:30',
            asr: '16:00',
            maghrib: '19:15',
            isha: '20:30'
        },
        'Asia/Riyadh': {
            fajr: '04:15',
            sunrise: '05:30',
            dhuhr: '12:15',
            asr: '15:45',
            maghrib: '19:00',
            isha: '20:15'
        }
        // يمكن إضافة المزيد من المدن هنا
    },
    
    // طريقة جامعة أم القرى، مكة المكرمة (Makkah)
    'Makkah': {
        'Asia/Amman': {
            fajr: '04:20',
            sunrise: '05:45',
            dhuhr: '12:30',
            asr: '16:00',
            maghrib: '19:15',
            isha: '21:00'
        },
        'Asia/Riyadh': {
            fajr: '04:05',
            sunrise: '05:30',
            dhuhr: '12:15',
            asr: '15:45',
            maghrib: '19:00',
            isha: '20:45'
        }
        // يمكن إضافة المزيد من المدن هنا
    },
    
    // طريقة مصر (Egypt)
    'Egypt': {
        'Asia/Amman': {
            fajr: '04:10',
            sunrise: '05:45',
            dhuhr: '12:30',
            asr: '16:00',
            maghrib: '19:15',
            isha: '20:50'
        },
        'Asia/Riyadh': {
            fajr: '03:55',
            sunrise: '05:30',
            dhuhr: '12:15',
            asr: '15:45',
            maghrib: '19:00',
            isha: '20:35'
        }
        // يمكن إضافة المزيد من المدن هنا
    }
};

// تعريف متغير عالمي لمواقيت الصلاة
window.prayerTimes = {};

// دالة للحصول على مواقيت الصلاة
function getPrayerTimes() {
    // الحصول على المدينة الحالية
    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    
    // الحصول على طريقة الحساب المحفوظة
    const savedMethod = localStorage.getItem('calculationMethod') || 'MWL';
    
    // الحصول على مواقيت الصلاة من البيانات المحددة مسبقًا
    let times;
    
    // التحقق من وجود طريقة الحساب المحددة
    if (PRAYER_TIMES_DATA[savedMethod]) {
        // التحقق من وجود المدينة المحددة
        if (PRAYER_TIMES_DATA[savedMethod][currentCity]) {
            times = PRAYER_TIMES_DATA[savedMethod][currentCity];
        } else {
            // إذا لم تكن المدينة موجودة، استخدم عمان كاحتياطي
            times = PRAYER_TIMES_DATA[savedMethod]['Asia/Amman'];
        }
    } else {
        // إذا لم تكن طريقة الحساب موجودة، استخدم MWL كاحتياطي
        if (PRAYER_TIMES_DATA['MWL'][currentCity]) {
            times = PRAYER_TIMES_DATA['MWL'][currentCity];
        } else {
            times = PRAYER_TIMES_DATA['MWL']['Asia/Amman'];
        }
    }
    
    // تحديث المواقيت العالمية للمدينة المحددة
    window.prayerTimes[currentCity] = times;
    
    return times;
}

// دالة لتحديث عرض مواقيت الصلاة
function updatePrayerTimesDisplay(times, format = '24') {
    if (!times) return;
    
    // تحديث عناصر HTML
    document.getElementById('fajr-time').textContent = times.fajr;
    document.getElementById('sunrise-time').textContent = times.sunrise;
    document.getElementById('dhuhr-time').textContent = times.dhuhr;
    document.getElementById('asr-time').textContent = times.asr;
    document.getElementById('maghrib-time').textContent = times.maghrib;
    document.getElementById('isha-time').textContent = times.isha;
}

// تصدير الدوال
window.getPrayerTimes = getPrayerTimes;
window.updatePrayerTimesDisplay = updatePrayerTimesDisplay;
window.PRAYER_TIMES_DATA = PRAYER_TIMES_DATA;
